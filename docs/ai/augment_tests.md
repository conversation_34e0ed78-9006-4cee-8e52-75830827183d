## **📋 Core Services Foundation Testing Plan**

### **🎯 Current State Analysis**

- **Overall Coverage**: 56% (improved from 44% mentioned in recovery plan)
- **Core Services Coverage Crisis**:
  - FileServiceCore: 14% coverage (146 lines)
  - MetadataHandlerCore: 18% coverage (174 lines)
  - ProcessingServiceCore: 21% coverage (225 lines)
  - PathServiceCore: 17% coverage (46 lines)
  - SettingsManagerCore: 38% coverage (72 lines)

### **🚀 Implementation Plan (7-Day Sprint)**

**Phase 1: Infrastructure Setup (Day 1)**
1. **Create proper test directory structure**:

   **Copy**

   tests/unit/services/core/

   ├── **init**.py

   ├── test_file_service_core.py

   ├── test_metadata_handler_core.py

   ├── test_processing_service_core.py

   ├── test_path_service_core.py

   └── test_settings_manager_core.py
2. **Create testing utilities** (`tests/utils/core_service_testing.py`):
   - `CallbackCapture`: Utility for capturing callback invocations
   - `ThreadSafetyTester`: Utilities for concurrent operation testing
   - `TempAudioFiles`: Fixtures for sample audio files
   - `MockFFmpeg`: Minimal FFmpeg mocking for ProcessingServiceCore

**Phase 2: FileServiceCore Testing (Days 1-2)**

**Target: 95%+ coverage (currently 14%)**

**Critical Test Coverage**:

**Copy**

class TestFileServiceCore:

    def test_add_files_validates_audio_formats(self):

        """Test only mp3, m4a, m4b, AAC files accepted."""

        

    def test_get_combined_size_accuracy(self):

        """Test combined size calculation correctness."""

        

    def test_reorder_files_maintains_integrity(self):

        """Test file order changes don't corrupt list."""

        

    def test_thread_safety_concurrent_operations(self):

        """Test concurrent add/remove operations are safe."""

        

    def test_callback_invocation_patterns(self):

        """Test success/error callbacks triggered correctly."""

        

    def test_file_validation_and_normalization(self):

        """Test path normalization and file existence validation."""

        

    def test_error_categorization(self):

        """Test proper ServiceError categorization."""

**Phase 3: MetadataHandlerCore Testing (Days 3-4)**

**Target: 95%+ coverage (currently 18%)**

**Critical Test Coverage**:

**Copy**

class TestMetadataHandlerCore:

    def test_load_from_file_extracts_all_metadata(self):

        """Test all metadata fields extracted from audio files."""

        

    def test_update_field_modifies_state_correctly(self):

        """Test metadata field updates persist correctly."""

        

    def test_get_for_ffmpeg_format_conversion(self):

        """Test FFmpeg metadata format mapping accuracy."""

        

    def test_cover_art_loading_and_validation(self):

        """Test cover art loading, validation, and storage."""

        

    def test_metadata_persistence_across_file_changes(self):

        """Test metadata survives file list reordering."""

        

    def test_state_management_and_callbacks(self):

        """Test internal state consistency and callback patterns."""

**Phase 4: ProcessingServiceCore Testing (Days 5-6)**

**Target: 95%+ coverage (currently 21%)**

**Critical Test Coverage**:

**Copy**

class TestProcessingServiceCore:

    def test_process_generates_correct_ffmpeg_command(self):

        """Test FFmpeg command generation with all settings."""

        

    def test_bitrate_passthrough_logic(self):

        """Test bitrate ≤64k passes through without re-encoding."""

        

    def test_progress_monitoring_accuracy(self):

        """Test progress callbacks report accurate completion."""

        

    def test_cancellation_cleanup(self):

        """Test process cancellation cleans up properly."""

        

    def test_error_categorization_and_propagation(self):

        """Test errors are categorized and propagated correctly."""

        

    def test_preview_generation(self):

        """Test preview mode generates correct output."""

**Phase 5: Supporting Services (Day 7)**

**PathServiceCore Testing** - Target: 95%+ coverage (currently 17%):

**Copy**

class TestPathServiceCore:

    def test_calculate_output_path_patterns(self):

        """Test subdirectory pattern generation."""

        

    def test_generate_filename_patterns(self):

        """Test filename pattern creation from metadata."""

        

    def test_character_sanitization(self):

        """Test invalid filesystem characters are replaced."""

**SettingsManagerCore Testing** - Target: 95%+ coverage (currently 38%):

**Copy**

class TestSettingsManagerCore:

    def test_setting_persistence_json_format(self):

        """Test settings persist to JSON correctly."""

        

    def test_setting_validation_and_types(self):

        """Test setting values are validated and typed."""

        

    def test_json_error_handling_recovery(self):

        """Test graceful handling of corrupted JSON files."""

### **🛠️ Testing Infrastructure Requirements**

**Key Testing Utilities:**

**Copy**

# tests/utils/core_service_testing.py

class CallbackCapture:

    """Utility for capturing and verifying callback invocations."""

    

class ThreadSafetyTester:

    """Utilities for testing concurrent operations."""

    

class TempAudioFiles:

    """Create temporary audio files for testing."""

# Fixtures

@pytest.fixture

def callback_capture():

    """Capture callback invocations for verification."""

    

@pytest.fixture  

def sample_audio_files(tmp_path):

    """Create temporary audio files for testing."""

    

@pytest.fixture

def mock_ffmpeg():

    """Mock FFmpeg subprocess calls consistently."""

### **📊 Success Criteria & Validation**

**Coverage Targets (Non-Negotiable):**
- **FileServiceCore**: &gt;95% coverage (from 14%)
- **MetadataHandlerCore**: &gt;95% coverage (from 18%)
- **ProcessingServiceCore**: &gt;95% coverage (from 21%)
- **PathServiceCore**: &gt;95% coverage (from 17%)
- **SettingsManagerCore**: &gt;95% coverage (from 38%)
- **Overall Core Services**: &gt;95% coverage

**Quality Gates:**
- All critical path behaviors tested (100% coverage)
- Thread safety validated on concurrent operations
- Error handling and categorization tested
- Callback patterns verified
- No regressions in existing test suite
- Average &lt;3 mocks per test file

**Validation Commands:**

**Copy**

# Core services coverage validation

pytest --cov=abb.services.core --cov-report=term-missing

pytest --cov=abb.services.core --cov-fail-under=95

# Full test suite (no regressions)

pytest

# Linting validation

ruff check tests/unit/services/core/

### **🎯 Implementation Priorities**

1. **Day 1**: Infrastructure + FileServiceCore (foundation patterns)
2. **Days 2-3**: MetadataHandlerCore (critical business logic)
3. **Days 4-5**: ProcessingServiceCore (most complex, FFmpeg integration)
4. **Day 6**: PathServiceCore (path generation logic)
5. **Day 7**: SettingsManagerCore + validation & cleanup

### **🔄 TDD Compliance Strategy**

Since the code already exists, I'll follow **"Test-Driven Recovery"**:
1. **Red**: Write failing tests for existing behavior
2. **Green**: Ensure tests pass with current implementation
3. **Refactor**: Improve test coverage and identify any bugs
4. **Validate**: Ensure &gt;95% coverage and no regressions

This plan focuses on the **most critical business logic first** (file management and metadata handling) while establishing robust testing patterns that can be applied to the remaining services. The approach prioritizes **behavior testing over implementation testing** and minimizes mocking to ensure tests validate real functionality.