# Generated on: 2025-06-07 17:47:35

This file is a merged representation of a subset of the codebase, containing specifically included files and files not matching ignore patterns, combined into a single document by Repomix.
The content has been processed where empty lines have been removed.

# File Summary

## Purpose
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

## File Format
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  a. A header with the file path (## File: path/to/file)
  b. The full contents of the file in a code block

## Usage Guidelines
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

## Notes
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: src/**
- Files matching these patterns are excluded: .html
- Files matching default ignore patterns are excluded
- Empty lines have been removed from all files
- Files are sorted by Git change count (files with more changes are at the bottom)

# Directory Structure
```
src/
  abb/
    controllers/
      __init__.py
      main_controller.py
    ffmpeg/
      command_builder.py
    services/
      adapters/
        __init__.py
        base_adapter.py
        file_service_adapter.py
        metadata_handler_adapter.py
        processing_service_adapter.py
        settings_manager_adapter.py
      core/
        __init__.py
        file_service_core.py
        metadata_handler_core.py
        path_service_core.py
        processing_service_core.py
        settings_manager_core.py
      __init__.py
      file_service.py
      interfaces.py
      metadata_service.py
      path_service.py
      processing_service.py
      service_factory.py
      settings_manager.py
      unified_metadata_handler.py
    ui/
      dialogs/
        __init__.py
        about_dialog.py
        settings_dialog.py
      widgets/
        __init__.py
        coverart.py
        dropzone.py
        left_panel_widget.py
        metadata_form_widget.py
        output_settings_widget.py
        right_panel_widget.py
      __init__.py
    __init__.py
    ffmpeg_utils.py
    main_window.py
    main.py
    metadata_utils.py
    processing_validator.py
    processing_worker.py
  __init__.py
  CLAUDE.local.md
```

# Files

## File: src/abb/controllers/__init__.py
```python

```

## File: src/abb/ui/dialogs/__init__.py
```python

```

## File: src/abb/ui/widgets/__init__.py
```python
"""Custom widgets for Audiobook Boss UI."""
```

## File: src/abb/ui/__init__.py
```python
"""UI package for Audiobook Boss."""
```

## File: src/abb/__init__.py
```python
"""Audiobook Boss (ABB) package."""
```

## File: src/__init__.py
```python
"""Source package for Audiobook Boss."""
```

## File: src/abb/services/adapters/__init__.py
```python
"""Qt Adapter layer for ABB services.
This module contains Qt adapter classes that wrap pure Python core services,
providing Qt signal-based interfaces while delegating business logic to
pure Python implementations.
The adapter pattern enables:
- Backwards compatibility with existing Qt-based UI code
- Feature flag-based migration between Qt and pure Python services
- Easier testing of business logic without Qt dependencies
- Cleaner separation between UI framework and business logic
Adapters implemented:
- QtFileServiceAdapter: File management with Qt signals
- QtSettingsManagerAdapter: Settings with Qt signal notifications
- QtMetadataHandlerAdapter: Metadata operations with Qt signals
- QtProcessingServiceAdapter: Audio processing with Qt progress signals
"""
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from .base_adapter import QtServiceAdapterBase
    from .file_service_adapter import QtFileServiceAdapter
    from .metadata_handler_adapter import QtMetadataHandlerAdapter
    from .processing_service_adapter import QtProcessingServiceAdapter
    from .settings_manager_adapter import QtSettingsManagerAdapter
from .base_adapter import QtServiceAdapterBase
from .processing_service_adapter import QtProcessingServiceAdapter
from .settings_manager_adapter import QtSettingsManagerAdapter
__all__ = [
    "QtServiceAdapterBase",
    "QtFileServiceAdapter",
    "QtSettingsManagerAdapter", 
    "QtMetadataHandlerAdapter",
    "QtProcessingServiceAdapter",
]
```

## File: src/abb/services/adapters/base_adapter.py
```python
"""Base adapter class for Qt service adapters.
This module provides QtServiceAdapterBase, a foundation class for all Qt service
adapters that bridge pure Python services with Qt signal/slot mechanisms.
"""
import logging
import threading
import weakref
from abc import ABC, abstractmethod
from concurrent.futures import ThreadPoolExecutor
from typing import Callable, Dict, Generic, List, Optional, Set, TypeVar
from PySide6.QtCore import QMetaObject, QObject, Qt, QThread, QTimer, Signal
logger = logging.getLogger(__name__)
# Type variables for generic service types
ServiceType = TypeVar('ServiceType')
CallbackType = Callable[..., None]
# Create a metaclass that combines Qt's metaclass with ABC
class QtAdapterMeta(type(QObject), type(ABC)):
    """Metaclass that resolves the conflict between Qt and ABC metaclasses."""
    pass
class QtServiceAdapterBase(QObject, Generic[ServiceType], metaclass=QtAdapterMeta):
    """Base class for Qt service adapters.
    Provides common patterns and utilities for bridging pure Python services
    with Qt signal/slot mechanisms. Handles thread safety, error propagation,
    callback management, and resource cleanup.
    Features:
    - Thread-safe signal emission
    - Callback registration and management
    - Error handling and propagation
    - Resource cleanup and memory management
    - Weak reference tracking to prevent cycles
    - Logging integration for debugging
    """
    # Common error signal that all adapters can use
    error_occurred = Signal(str, str)  # error_type, error_message
    # Status signals for adapter lifecycle
    adapter_initialized = Signal()
    adapter_destroyed = Signal()
    def __init__(self, core_service: ServiceType, parent: Optional[QObject] = None):
        """Initialize the Qt service adapter.
        Args:
            core_service: The pure Python service to adapt
            parent: Optional Qt parent object
        """
        super().__init__(parent)
        # Store weak reference to core service to prevent cycles
        self._core_service_ref = weakref.ref(core_service)
        # Callback management
        self._callbacks: Dict[str, List[weakref.ref]] = {}
        self._callback_lock = threading.RLock()
        # Error handling
        self._error_handlers: Set[weakref.ref] = set()
        # Thread safety
        self._signal_queue_timer = QTimer()
        self._signal_queue_timer.timeout.connect(self._process_queued_signals)
        self._signal_queue_timer.setSingleShot(True)
        self._signal_queue: List[tuple] = []
        self._signal_queue_lock = threading.Lock()
        # Resource tracking
        self._is_destroyed = False
        self._cleanup_callbacks: List[Callable[[], None]] = []
        # Thread pool for async operations
        self._thread_pool = ThreadPoolExecutor(max_workers=2, thread_name_prefix="QtAdapter")
        # Setup logging context
        self._logger = logger.getChild(self.__class__.__name__)
        self._logger.debug(f"Initialized {self.__class__.__name__}")
        self.adapter_initialized.emit()
    @property
    def core_service(self) -> Optional[ServiceType]:
        """Get the core service, returns None if it has been garbage collected."""
        return self._core_service_ref()
    @property
    def is_destroyed(self) -> bool:
        """Check if the adapter has been destroyed."""
        return self._is_destroyed
    def register_callback(self, event_name: str, callback: CallbackType) -> None:
        """Register a callback for a specific event.
        Args:
            event_name: Name of the event to listen for
            callback: Callback function to register
        """
        if self._is_destroyed:
            self._logger.warning(f"Attempted to register callback on destroyed adapter: {event_name}")
            return
        with self._callback_lock:
            if event_name not in self._callbacks:
                self._callbacks[event_name] = []
            # Store weak reference to prevent memory leaks
            callback_ref = weakref.ref(callback)
            self._callbacks[event_name].append(callback_ref)
            self._logger.debug(f"Registered callback for event: {event_name}")
    def unregister_callback(self, event_name: str, callback: CallbackType) -> bool:
        """Unregister a callback for a specific event.
        Args:
            event_name: Name of the event
            callback: Callback function to unregister
        Returns:
            True if callback was found and removed, False otherwise
        """
        with self._callback_lock:
            if event_name not in self._callbacks:
                return False
            # Find and remove the callback
            for i, callback_ref in enumerate(self._callbacks[event_name]):
                if callback_ref() is callback:
                    del self._callbacks[event_name][i]
                    self._logger.debug(f"Unregistered callback for event: {event_name}")
                    return True
            return False
    def emit_to_callbacks(self, event_name: str, *args, **kwargs) -> None:
        """Emit an event to all registered callbacks.
        Args:
            event_name: Name of the event to emit
            *args: Positional arguments for callbacks
            **kwargs: Keyword arguments for callbacks
        """
        if self._is_destroyed:
            return
        with self._callback_lock:
            if event_name not in self._callbacks:
                return
            # Clean up dead references and call live ones
            live_callbacks = []
            for callback_ref in self._callbacks[event_name]:
                callback = callback_ref()
                if callback is not None:
                    live_callbacks.append(callback_ref)
                    try:
                        callback(*args, **kwargs)
                    except Exception as e:
                        self._handle_callback_error(event_name, callback, e)
            # Update the list to remove dead references
            self._callbacks[event_name] = live_callbacks
    def emit_signal_safe(self, signal: Signal, *args) -> None:
        """Thread-safe signal emission.
        If called from the main thread, emits immediately.
        If called from another thread, queues for emission on main thread.
        Args:
            signal: Qt signal to emit
            *args: Arguments for the signal
        """
        if self._is_destroyed:
            return
        if QThread.currentThread() == self.thread():
            # We're on the main thread, emit directly
            try:
                signal.emit(*args)
            except Exception as e:
                self._logger.error(f"Error emitting signal {signal}: {e}")
                self._emit_error("signal_emission", str(e))
        else:
            # We're on a different thread, queue for main thread
            with self._signal_queue_lock:
                self._signal_queue.append((signal, args))
            # Use QMetaObject to invoke timer start on main thread
            QMetaObject.invokeMethod(
                self._signal_queue_timer,
                "start",
                Qt.ConnectionType.QueuedConnection,
                10  # 10ms delay
            )
    def _process_queued_signals(self) -> None:
        """Process queued signals on the main thread."""
        with self._signal_queue_lock:
            signals_to_emit = self._signal_queue.copy()
            self._signal_queue.clear()
        for signal, args in signals_to_emit:
            try:
                signal.emit(*args)
            except Exception as e:
                self._logger.error(f"Error emitting queued signal {signal}: {e}")
                self._emit_error("queued_signal_emission", str(e))
    def handle_service_error(self, error: Exception, context: str = "") -> None:
        """Handle errors from the core service.
        Args:
            error: The exception that occurred
            context: Additional context about where the error occurred
        """
        error_msg = f"{context}: {str(error)}" if context else str(error)
        error_type = error.__class__.__name__
        self._logger.error(f"Service error [{error_type}]: {error_msg}")
        self._emit_error(error_type, error_msg)
        # Call registered error handlers
        for handler_ref in list(self._error_handlers):
            handler = handler_ref()
            if handler is not None:
                try:
                    handler(error, context)
                except Exception as e:
                    self._logger.error(f"Error in error handler: {e}")
            else:
                # Remove dead reference
                self._error_handlers.discard(handler_ref)
    def register_error_handler(self, handler: Callable[[Exception, str], None]) -> None:
        """Register an error handler for service errors.
        Args:
            handler: Function to call when errors occur
        """
        self._error_handlers.add(weakref.ref(handler))
    def add_cleanup_callback(self, callback: Callable[[], None]) -> None:
        """Add a callback to be called during cleanup.
        Args:
            callback: Function to call during cleanup
        """
        self._cleanup_callbacks.append(callback)
    def run_async(self, func: Callable, *args, **kwargs) -> None:
        """Run a function asynchronously in the thread pool.
        Args:
            func: Function to run
            *args: Positional arguments
            **kwargs: Keyword arguments
        """
        if self._is_destroyed:
            return
        def wrapped_func():
            try:
                return func(*args, **kwargs)
            except Exception as e:
                self._logger.error(f"Error in async function {func.__name__}: {e}")
                self.emit_signal_safe(self.error_occurred, "async_error", str(e))
        self._thread_pool.submit(wrapped_func)
    def _emit_error(self, error_type: str, error_message: str) -> None:
        """Emit error signal in a thread-safe manner.
        Args:
            error_type: Type/category of the error
            error_message: Detailed error message
        """
        self.emit_signal_safe(self.error_occurred, error_type, error_message)
    def _handle_callback_error(self, event_name: str, callback: CallbackType, error: Exception) -> None:
        """Handle errors that occur in callbacks.
        Args:
            event_name: Name of the event that triggered the callback
            callback: The callback that failed
            error: The exception that occurred
        """
        callback_name = getattr(callback, '__name__', str(callback))
        error_msg = f"Callback {callback_name} for event {event_name} failed: {error}"
        self._logger.error(error_msg)
        self._emit_error("callback_error", error_msg)
    @abstractmethod
    def initialize_adapter(self) -> None:
        """Initialize the adapter's specific functionality.
        This method should be implemented by subclasses to set up
        their specific signal connections and service integrations.
        """
        pass
    @abstractmethod
    def cleanup_adapter(self) -> None:
        """Clean up adapter-specific resources.
        This method should be implemented by subclasses to clean up
        their specific resources and connections.
        """
        pass
    def cleanup(self) -> None:
        """Clean up all adapter resources.
        This method handles the complete cleanup of the adapter,
        including calling subclass cleanup methods and clearing
        all internal state.
        """
        if self._is_destroyed:
            return
        self._logger.debug(f"Starting cleanup of {self.__class__.__name__}")
        self._is_destroyed = True
        try:
            # Call subclass cleanup
            self.cleanup_adapter()
        except Exception as e:
            self._logger.error(f"Error in adapter cleanup: {e}")
        # Run cleanup callbacks
        for callback in self._cleanup_callbacks:
            try:
                callback()
            except Exception as e:
                self._logger.error(f"Error in cleanup callback: {e}")
        # Clean up internal state
        with self._callback_lock:
            self._callbacks.clear()
        self._error_handlers.clear()
        self._cleanup_callbacks.clear()
        # Stop and clean up timer
        if self._signal_queue_timer.isActive():
            self._signal_queue_timer.stop()
        # Clean up signal queue
        with self._signal_queue_lock:
            self._signal_queue.clear()
        # Shutdown thread pool
        self._thread_pool.shutdown(wait=False)
        self._logger.debug(f"Completed cleanup of {self.__class__.__name__}")
        self.adapter_destroyed.emit()
    def __del__(self):
        """Ensure cleanup is called when adapter is garbage collected."""
        if not self._is_destroyed:
            self.cleanup()
class QtServiceAdapterMixin:
    """Mixin class providing common adapter utilities.
    Can be used with adapters that don't inherit from QtServiceAdapterBase
    but still need some of its functionality.
    """
    @staticmethod
    def create_error_handler(adapter: QtServiceAdapterBase, 
                           error_signal: Signal) -> Callable[[Exception, str], None]:
        """Create a standard error handler that emits to a specific signal.
        Args:
            adapter: The adapter instance
            error_signal: Signal to emit errors to
        Returns:
            Error handler function
        """
        def handle_error(error: Exception, context: str = ""):
            error_type = error.__class__.__name__
            error_msg = f"{context}: {str(error)}" if context else str(error)
            adapter.emit_signal_safe(error_signal, error_type, error_msg)
        return handle_error
    @staticmethod
    def create_callback_wrapper(adapter: QtServiceAdapterBase,
                               signal: Signal) -> Callable[..., None]:
        """Create a callback wrapper that safely emits to a Qt signal.
        Args:
            adapter: The adapter instance
            signal: Signal to emit to
        Returns:
            Callback function that emits to the signal
        """
        def callback_wrapper(*args, **kwargs):
            # Convert kwargs to args if needed, as Qt signals don't handle kwargs well
            if kwargs:
                # Log warning about kwargs being dropped
                adapter._logger.warning(f"Dropping kwargs in signal emission: {kwargs}")
            adapter.emit_signal_safe(signal, *args)
        return callback_wrapper
```

## File: src/abb/services/adapters/file_service_adapter.py
```python
"""QtFileServiceAdapter - Qt adapter for FileServiceCore.
This module provides a Qt adapter that wraps a FileServiceCore instance,
maintaining complete backwards compatibility with the existing FileService
while delegating business logic to the pure Python core service.
"""
from typing import List, Optional
from PySide6.QtCore import QObject, QTimer, Signal
from ..core.file_service_core import FileServiceCore
class QtFileServiceAdapter(QObject):
    """Qt adapter for FileServiceCore that maintains FileService compatibility.
    This adapter wraps a FileServiceCore instance and provides the same Qt signals
    and method signatures as the original FileService, enabling seamless migration
    between Qt-dependent and pure Python service implementations.
    Key features:
    - Complete backwards compatibility with existing FileService interface
    - Thread-safe signal emissions using Qt's queued connections
    - Automatic callback-to-signal translation
    - Error handling that maintains existing behavior patterns
    - No data copying overhead in method delegation
    """
    # Qt signals - identical to original FileService
    files_changed = Signal(list)                # Signal emitted when the file list changes
    combined_size_changed_signal = Signal(str)  # Signal for combined size updates
    def __init__(self, core_service: Optional[FileServiceCore] = None):
        """Initialize the adapter with a FileServiceCore instance.
        Args:
            core_service: FileServiceCore instance to wrap. If None, creates a new one.
        """
        super().__init__()
        self._core = core_service or FileServiceCore()
        # Initialize timers as None, will be created if needed
        self._files_changed_timer: Optional[QTimer] = None
        self._size_changed_timer: Optional[QTimer] = None
        # Cached data for signal emission
        self._cached_files: List[str] = []
        self._cached_size: str = ""
    def get_files(self) -> List[str]:
        """Get the current list of files.
        Returns:
            List of file paths currently managed by the service.
        """
        return self._core.get_files()
    def get_combined_size(self) -> int:
        """Get the combined size of all files in bytes.
        Returns:
            Total size in bytes of all managed files.
        """
        return self._core.get_combined_size()
    def format_combined_size(self) -> str:
        """Get the combined size formatted for display.
        Returns:
            Human-readable size string (e.g., "1.5 MB", "2.3 GB").
        """
        return self._core.format_combined_size()
    def get_file_count(self) -> int:
        """Get the number of files currently managed.
        Returns:
            Number of files in the managed list.
        """
        return self._core.get_file_count()
    def clear_files(self) -> None:
        """Clear all files from the managed list.
        Maintains backwards compatibility by not requiring callbacks.
        """
        def on_success() -> None:
            self._queue_files_changed_signal()
            self._queue_size_changed_signal()
        def on_error(error_msg: str) -> None:
            # For backwards compatibility, log error but don't raise
            # The original FileService didn't have error handling for clear
            print(f"Warning: Failed to clear files: {error_msg}")
        self._core.clear_files(on_success, on_error)
    def add_files(self, paths: List[str]) -> List[str]:
        """Add files to the list, normalizing paths and filtering by extension.
        Args:
            paths: List of file paths to add
        Returns:
            List of file paths that were actually added
        """
        added_files: List[str] = []
        def on_success(successfully_added: List[str]) -> None:
            nonlocal added_files
            added_files = successfully_added
            if successfully_added:
                self._queue_files_changed_signal()
                self._queue_size_changed_signal()
        def on_error(error_msg: str) -> None:
            # For backwards compatibility, don't raise errors during add_files
            # The original FileService silently skipped problematic files
            pass
        # Call core service - callbacks are called synchronously
        self._core.add_files(paths, on_success, on_error)
        return added_files
    def remove_file(self, index: int) -> None:
        """Remove a file from the list by index.
        Args:
            index: Index of the file to remove
        """
        def on_success() -> None:
            self._queue_files_changed_signal()
            self._queue_size_changed_signal()
        def on_error(error_msg: str) -> None:
            # For backwards compatibility, silently handle invalid indices
            # The original FileService had bounds checking but no error propagation
            pass
        self._core.remove_file(index, on_success, on_error)
    def reorder_files(self, new_order_paths: list) -> None:
        """Reorder files by setting a new order based on provided file paths.
        Args:
            new_order_paths: List of file paths representing the new order
        """
        def on_success() -> None:
            self._queue_files_changed_signal()
            self._queue_size_changed_signal()
        def on_error(error_msg: str) -> None:
            # For backwards compatibility, silently handle reorder errors
            # The original FileService assumed valid input
            pass
        self._core.reorder_files(new_order_paths, on_success, on_error)
    def _queue_files_changed_signal(self) -> None:
        """Queue emission of files_changed signal in a thread-safe manner."""
        self._cached_files = self._core.get_files()
        # Try to use QTimer if possible, otherwise emit directly
        try:
            if self._files_changed_timer is None:
                self._files_changed_timer = QTimer()
                self._files_changed_timer.setSingleShot(True)
                self._files_changed_timer.timeout.connect(self._emit_files_changed)
            if not self._files_changed_timer.isActive():
                self._files_changed_timer.start(0)
        except (RuntimeError, AttributeError):
            # Qt event loop not running or other issue, emit signal directly
            self._emit_files_changed()
    def _queue_size_changed_signal(self) -> None:
        """Queue emission of combined_size_changed_signal in a thread-safe manner."""
        self._cached_size = self._core.format_combined_size()
        # Try to use QTimer if possible, otherwise emit directly
        try:
            if self._size_changed_timer is None:
                self._size_changed_timer = QTimer()
                self._size_changed_timer.setSingleShot(True)
                self._size_changed_timer.timeout.connect(self._emit_size_changed)
            if not self._size_changed_timer.isActive():
                self._size_changed_timer.start(0)
        except (RuntimeError, AttributeError):
            # Qt event loop not running or other issue, emit signal directly
            self._emit_size_changed()
    def _emit_files_changed(self) -> None:
        """Emit the files_changed signal with cached data."""
        # Create a copy to match original FileService behavior
        self.files_changed.emit(self._cached_files.copy())
    def _emit_size_changed(self) -> None:
        """Emit the combined_size_changed_signal with cached formatted size."""
        self.combined_size_changed_signal.emit(self._cached_size)
    def validate_files(self) -> None:
        """Validate that all managed files still exist and are accessible.
        Removes any files that are no longer accessible and emits signals if changes occur.
        This is an additional method not in the original FileService but useful for adapters.
        """
        def on_success(removed_files: List[str]) -> None:
            if removed_files:
                # Files were removed, emit change signals
                self._queue_files_changed_signal()
                self._queue_size_changed_signal()
        def on_error(error_msg: str) -> None:
            # Log validation errors but don't propagate them
            print(f"Warning: File validation failed: {error_msg}")
        self._core.validate_files(on_success, on_error)
    # Private methods that mirror the original FileService for internal consistency
    def _emit_combined_size(self) -> None:
        """Legacy method name for backwards compatibility.
        This method maintains the same name as the original FileService
        to ensure any subclasses or extensions continue to work.
        """
        self._queue_size_changed_signal()
    def _normalize_path(self, path: str) -> str:
        """Normalize a file path to its absolute, canonical form.
        This delegates to the core service's normalization logic.
        Maintained for backwards compatibility with any code that calls this directly.
        Args:
            path: File path to normalize
        Returns:
            Normalized path
        Raises:
            ServiceError: If path cannot be normalized
        """
        return self._core._normalize_path(path)
    def _is_valid_audio_file(self, path: str) -> bool:
        """Check if a file has a valid audio extension.
        This delegates to the core service's validation logic.
        Maintained for backwards compatibility.
        Args:
            path: File path to check
        Returns:
            True if the file has a valid audio extension, False otherwise
        """
        return self._core._is_valid_audio_file(path)
```

## File: src/abb/services/adapters/metadata_handler_adapter.py
```python
"""Qt adapter for MetadataHandlerCore service.
This adapter provides Qt signal-based interfaces while delegating all metadata
operations to the pure Python MetadataHandlerCore implementation.
"""
import logging
from typing import Any, Dict, Optional
from PySide6.QtCore import Signal
from ..core.metadata_handler_core import MetadataHandlerCore
from .base_adapter import QtServiceAdapterBase
logger = logging.getLogger(__name__)
class QtMetadataHandlerAdapter(QtServiceAdapterBase[MetadataHandlerCore]):
    """Qt adapter for MetadataHandlerCore providing signal-based communication.
    This adapter maintains complete backwards compatibility with the existing
    MetadataService while delegating all business logic to MetadataHandlerCore.
    Signals:
        metadata_loaded: Emitted when metadata is loaded from a file
        metadata_updated: Emitted when metadata fields are updated
        metadata_error: Emitted when errors occur during operations
    """
    # Qt signals matching original MetadataService
    metadata_loaded = Signal(dict)    # File loading operations
    metadata_updated = Signal(dict)   # Metadata changes and updates
    metadata_error = Signal(str)      # Error handling
    def __init__(self, core_service: Optional[MetadataHandlerCore] = None):
        """Initialize the metadata handler adapter.
        Args:
            core_service: Optional MetadataHandlerCore instance. If not provided,
                         a new instance will be created.
        """
        # Create core service if not provided
        if core_service is None:
            core_service = MetadataHandlerCore()
        super().__init__(core_service)
        # Initialize state properties for compatibility
        self.current_metadata: Optional[dict] = None
        self.current_cover_art_data: Optional[bytes] = None
        self.current_cover_art_path: Optional[str] = None
    def initialize_adapter(self) -> None:
        """Initialize the adapter by setting up core service callbacks."""
        # The core service methods will be called directly with callbacks
        # No persistent callback registration needed for MetadataHandlerCore
        logger.debug("QtMetadataHandlerAdapter initialized")
    def cleanup_adapter(self) -> None:
        """Clean up adapter-specific resources."""
        # Clear state
        self.current_metadata = None
        self.current_cover_art_data = None
        self.current_cover_art_path = None
        logger.debug("QtMetadataHandlerAdapter cleaned up")
    def get_metadata(self) -> Dict[str, Any]:
        """Get current metadata dictionary.
        Returns:
            Copy of current metadata dictionary
        """
        try:
            core_service = self.get_core_service()
            if core_service:
                metadata = core_service.get_current_metadata()
                self.current_metadata = metadata.copy() if metadata else None
                return metadata
            return {}
        except Exception as e:
            self.handle_service_error(e, "get_metadata")
            return {}
    def extract_metadata(self, file_path: str) -> Dict[str, Any]:
        """Extract metadata from an audio file.
        This method is synchronous for backwards compatibility but uses
        the core service's async capabilities internally.
        Args:
            file_path: Path to audio file
        Returns:
            Extracted metadata dictionary
        """
        try:
            core_service = self.get_core_service()
            if not core_service:
                return {}
            result = {}
            error = None
            def on_success(metadata: Dict[str, Any]) -> None:
                nonlocal result
                result = metadata
            def on_error(error_msg: str) -> None:
                nonlocal error
                error = error_msg
            # Call core service method
            core_service.load_from_file(file_path, on_success, on_error)
            if error:
                self.emit_signal_safe(self.metadata_error, error)
                return {}
            return result
        except Exception as e:
            self.handle_service_error(e, "extract_metadata")
            return {}
    def apply_defaults(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Apply default values to metadata.
        Args:
            metadata: Metadata dictionary to apply defaults to
        Returns:
            Metadata with defaults applied
        """
        try:
            core_service = self.get_core_service()
            if not core_service:
                return metadata
            # Update core service metadata and apply defaults
            for field, value in metadata.items():
                core_service.update_field(field, value, lambda _: None)
            # Apply defaults
            result = {}
            def on_success(updated_metadata: Dict[str, Any]) -> None:
                nonlocal result
                result = updated_metadata
            core_service.apply_defaults(on_success)
            return result
        except Exception as e:
            self.handle_service_error(e, "apply_defaults")
            return metadata
    def update_metadata_field(self, field: str, value: Any) -> None:
        """Update a single metadata field.
        Args:
            field: Field name to update
            value: New value for the field
        """
        try:
            core_service = self.get_core_service()
            if not core_service:
                return
            def on_success(metadata: Dict[str, Any]) -> None:
                self.current_metadata = metadata.copy()
                self.emit_signal_safe(self.metadata_updated, metadata)
            def on_error(error_msg: str) -> None:
                self.emit_signal_safe(self.metadata_error, error_msg)
            core_service.update_field(field, value, on_success, on_error)
        except Exception as e:
            self.handle_service_error(e, "update_metadata_field")
    def clear_metadata(self) -> None:
        """Clear all metadata."""
        try:
            core_service = self.get_core_service()
            if not core_service:
                return
            def on_success() -> None:
                self.current_metadata = None
                self.current_cover_art_data = None
                self.current_cover_art_path = None
                self.emit_signal_safe(self.metadata_updated, {})
            def on_error(error_msg: str) -> None:
                self.emit_signal_safe(self.metadata_error, error_msg)
            core_service.clear_metadata(on_success, on_error)
        except Exception as e:
            self.handle_service_error(e, "clear_metadata")
    def extract_and_load_metadata(self, filepath: str) -> None:
        """Extract metadata and cover art from a file and load it.
        This method combines metadata extraction and cover art extraction,
        emitting appropriate signals for UI updates.
        Args:
            filepath: Path to audio file
        """
        try:
            core_service = self.get_core_service()
            if not core_service:
                return
            def on_metadata_loaded(metadata: Dict[str, Any]) -> None:
                self.current_metadata = metadata.copy()
                # Extract cover art
                cover_data = core_service.get_cover_art_data()
                self.current_cover_art_data = cover_data
                self.current_cover_art_path = core_service.get_cover_art_path()
                # Emit signal with metadata
                self.emit_signal_safe(self.metadata_loaded, metadata)
            def on_error(error_msg: str) -> None:
                logger.error(f"Failed to extract metadata from {filepath}: {error_msg}")
                self.emit_signal_safe(self.metadata_error, error_msg)
            # Load metadata from file
            core_service.load_from_file(filepath, on_metadata_loaded, on_error)
        except Exception as e:
            self.handle_service_error(e, "extract_and_load_metadata")
    def update_current_metadata(self, field_name: str, new_value: str) -> None:
        """Update current metadata field and emit signal.
        Args:
            field_name: Metadata field to update
            new_value: New value for the field
        """
        # This is an alias for update_metadata_field for compatibility
        self.update_metadata_field(field_name, new_value)
    def set_cover_art(self, image_path: str) -> None:
        """Set cover art from image file path.
        Args:
            image_path: Path to cover art image file
        """
        try:
            core_service = self.get_core_service()
            if not core_service:
                return
            def on_success(path: Optional[str]) -> None:
                self.current_cover_art_path = path
                # Get updated metadata
                metadata = core_service.get_current_metadata()
                self.current_metadata = metadata.copy()
                self.emit_signal_safe(self.metadata_updated, metadata)
            def on_error(error_msg: str) -> None:
                self.emit_signal_safe(self.metadata_error, error_msg)
            core_service.set_cover_art(image_path, on_success, on_error)
        except Exception as e:
            self.handle_service_error(e, "set_cover_art")
    def get_cover_art_path(self) -> Optional[str]:
        """Get current cover art file path.
        Returns:
            Path to cover art file or None if no cover art
        """
        try:
            core_service = self.get_core_service()
            if core_service:
                path = core_service.get_cover_art_path()
                self.current_cover_art_path = path
                return path
            return None
        except Exception as e:
            self.handle_service_error(e, "get_cover_art_path")
            return None
    def handle_service_error(self, error: Exception, context: str = "") -> None:
        """Handle errors from core service operations.
        Args:
            error: The exception that occurred
            context: Context information about where the error occurred
        """
        error_msg = str(error)
        if context:
            error_msg = f"{context}: {error_msg}"
        logger.error(f"QtMetadataHandlerAdapter error: {error_msg}")
        self.emit_signal_safe(self.metadata_error, error_msg)
        # Call parent error handler
        super().handle_service_error(error, context)
```

## File: src/abb/services/adapters/processing_service_adapter.py
```python
"""Qt adapter for ProcessingServiceCore service.
This adapter provides Qt signal-based interfaces while delegating all processing
operations to the pure Python ProcessingServiceCore implementation.
"""
import logging
from typing import Any, Dict, List, Optional
from PySide6.QtCore import Signal
from ..core.path_service_core import PathServiceCore
from ..core.processing_service_core import ProcessingServiceCore
from ..interfaces import IPathService
from .base_adapter import QtServiceAdapterBase
logger = logging.getLogger(__name__)
class QtProcessingServiceAdapter(QtServiceAdapterBase[ProcessingServiceCore]):
    """Qt adapter for ProcessingServiceCore providing signal-based communication.
    This adapter maintains complete backwards compatibility with the existing
    ProcessingService while delegating all business logic to ProcessingServiceCore.
    The adapter handles the complex threading, progress reporting, and cancellation
    patterns required for audio processing operations.
    Signals:
        progress: Emitted with percentage (0-100) during processing
        finished: Emitted with output file path on successful completion
        error: Emitted with error message on processing failure
        status: Emitted with status messages during processing
    """
    # Qt signals matching original ProcessingService
    progress = Signal(int)           # Progress percentage 0-100
    finished = Signal(str)           # Output file path on completion
    error = Signal(str)              # Error messages
    status = Signal(str)             # Status updates during processing
    def __init__(self, core_service: Optional[ProcessingServiceCore] = None,
                 path_service: Optional[IPathService] = None):
        """Initialize the processing service adapter.
        Args:
            core_service: Optional ProcessingServiceCore instance. If not provided,
                         a new instance will be created with the path_service.
            path_service: Optional IPathService implementation. If not provided,
                         a new PathServiceCore will be created.
        """
        # Create path service if not provided
        if path_service is None:
            path_service = PathServiceCore()
        # Create core service if not provided
        if core_service is None:
            core_service = ProcessingServiceCore(path_service)
        super().__init__(core_service)
        # Initialize state tracking
        self._is_processing = False
        self._current_operation = None
    def initialize_adapter(self) -> None:
        """Initialize the adapter by setting up core service callbacks."""
        logger.debug("QtProcessingServiceAdapter initialized")
    def cleanup_adapter(self) -> None:
        """Clean up adapter-specific resources."""
        # Cancel any ongoing processing
        if self._is_processing:
            self.cancel()
        logger.debug("QtProcessingServiceAdapter cleaned up")
    def process_full(self, input_files: List[str], output_path: str,
                     output_filename: str, metadata: Dict[str, Any],
                     settings: Dict[str, Any]) -> None:
        """Process audio files into audiobook format (full processing).
        This method delegates to the core service's process method with
        appropriate callback registration for Qt signal emission.
        Args:
            input_files: List of input audio file paths
            output_path: Directory for output file
            output_filename: Name of output file
            metadata: Metadata to embed in output
            settings: Processing settings (bitrate, format, etc.)
        """
        try:
            core_service = self.get_core_service()
            if not core_service:
                return
            if self._is_processing:
                self.emit_signal_safe(self.error, "Processing already in progress")
                return
            self._is_processing = True
            self._current_operation = "full"
            # Define callbacks
            def on_progress(percentage: int) -> None:
                self.emit_signal_safe(self.progress, percentage)
            def on_status(message: str) -> None:
                self.emit_signal_safe(self.status, message)
            def on_complete(output_file: str) -> None:
                self._is_processing = False
                self._current_operation = None
                self.emit_signal_safe(self.finished, output_file)
            def on_error(error_msg: str) -> None:
                self._is_processing = False
                self._current_operation = None
                self.emit_signal_safe(self.error, error_msg)
            # Start processing
            core_service.process(
                input_files=input_files,
                output_path=output_path,
                output_filename=output_filename,
                metadata=metadata,
                settings=settings,
                progress_callback=on_progress,
                status_callback=on_status,
                complete_callback=on_complete,
                error_callback=on_error
            )
        except Exception as e:
            self._is_processing = False
            self._current_operation = None
            self.handle_service_error(e, "process_full")
    def start_processing(self, file_list: List[str], output_settings: Dict[str, Any],
                        metadata: Dict[str, Any], cover_art_path: Optional[str] = None) -> None:
        """Start processing with validation and setup (main entry point).
        This method performs validation and setup before delegating to process_full.
        It maintains compatibility with the original ProcessingService interface.
        Args:
            file_list: List of input audio files
            output_settings: Dictionary containing output configuration
            metadata: Metadata to embed
            cover_art_path: Optional path to cover art image
        """
        try:
            # Extract settings
            output_dir = output_settings.get("output_directory", "")
            output_filename = output_settings.get("output_filename", "output.m4b")
            # Add cover art to metadata if provided
            if cover_art_path:
                metadata = metadata.copy()
                metadata["cover_art_path"] = cover_art_path
            # Process settings for core service
            settings = {
                "bitrate": output_settings.get("output_bitrate", 64),
                "sample_rate": output_settings.get("output_sample_rate", "auto"),
                "channels": output_settings.get("output_channels", 1),
                "use_subdirectory": output_settings.get("output_create_subdirectory", True),
                "filename_pattern": output_settings.get("output_filename_pattern", 0),
            }
            # Delegate to process_full
            self.process_full(
                input_files=file_list,
                output_path=output_dir,
                output_filename=output_filename,
                metadata=metadata,
                settings=settings
            )
        except Exception as e:
            self.handle_service_error(e, "start_processing")
    def process_preview(self, input_file_path: str, metadata: Dict[str, Any],
                       settings: Dict[str, Any], temp_cover_path: Optional[str] = None,
                       duration_seconds: int = 30) -> None:
        """Generate a preview of the processed audio.
        Args:
            input_file_path: Path to input audio file
            metadata: Metadata to embed
            settings: Processing settings
            temp_cover_path: Optional temporary cover art path
            duration_seconds: Length of preview in seconds
        """
        try:
            core_service = self.get_core_service()
            if not core_service:
                return
            if self._is_processing:
                self.emit_signal_safe(self.error, "Processing already in progress")
                return
            self._is_processing = True
            self._current_operation = "preview"
            # Add cover art to metadata if provided
            if temp_cover_path:
                metadata = metadata.copy()
                metadata["cover_art_path"] = temp_cover_path
            # Define callbacks
            def on_complete(output_file: str) -> None:
                self._is_processing = False
                self._current_operation = None
                self.emit_signal_safe(self.finished, output_file)
            def on_error(error_msg: str) -> None:
                self._is_processing = False
                self._current_operation = None
                self.emit_signal_safe(self.error, error_msg)
            # Start preview processing
            core_service.process_preview(
                input_file=input_file_path,
                metadata=metadata,
                settings=settings,
                duration_seconds=duration_seconds,
                complete_callback=on_complete,
                error_callback=on_error
            )
        except Exception as e:
            self._is_processing = False
            self._current_operation = None
            self.handle_service_error(e, "process_preview")
    def cancel(self) -> None:
        """Cancel current processing operation."""
        try:
            core_service = self.get_core_service()
            if not core_service:
                return
            def on_success() -> None:
                self._is_processing = False
                self._current_operation = None
                self.emit_signal_safe(self.status, "Processing cancelled")
            def on_error(error_msg: str) -> None:
                logger.error(f"Failed to cancel processing: {error_msg}")
                # Still mark as not processing even if cancel fails
                self._is_processing = False
                self._current_operation = None
            core_service.cancel(on_success, on_error)
        except Exception as e:
            self._is_processing = False
            self._current_operation = None
            self.handle_service_error(e, "cancel")
    def cleanup_worker_resources(self) -> None:
        """Clean up worker thread resources.
        This method ensures proper cleanup of background threads and processes.
        It's called during service shutdown or when processing is complete.
        """
        try:
            # Cancel any ongoing processing
            if self._is_processing:
                self.cancel()
            # Core service handles its own cleanup
            core_service = self.get_core_service()
            if core_service:
                # ProcessingServiceCore cleans up in its own methods
                logger.debug("Worker resources cleaned up via core service")
        except Exception as e:
            logger.error(f"Error cleaning up worker resources: {e}")
    def is_processing(self) -> bool:
        """Check if processing is currently active.
        Returns:
            True if processing is active, False otherwise
        """
        try:
            core_service = self.get_core_service()
            if core_service:
                return core_service.is_processing()
            return False
        except Exception:
            return self._is_processing
    def handle_service_error(self, error: Exception, context: str = "") -> None:
        """Handle errors from core service operations.
        Args:
            error: The exception that occurred
            context: Context information about where the error occurred
        """
        error_msg = str(error)
        if context:
            error_msg = f"{context}: {error_msg}"
        logger.error(f"QtProcessingServiceAdapter error: {error_msg}")
        self.emit_signal_safe(self.error, error_msg)
        # Reset processing state on error
        self._is_processing = False
        self._current_operation = None
        # Call parent error handler
        super().handle_service_error(error, context)
    def _get_ffmpeg_path(self) -> str:
        """Get FFmpeg executable path.
        This method is for compatibility with code that might access it directly.
        The actual FFmpeg path is managed by the core service.
        Returns:
            Path to FFmpeg executable
        """
        # Import here to avoid circular dependency
        from ..ffmpeg_utils import _get_executable_path
        return _get_executable_path("ffmpeg")
```

## File: src/abb/services/adapters/settings_manager_adapter.py
```python
"""Qt adapter for SettingsManagerCore that provides Qt signal-based interface."""
import logging
from typing import Any
from PySide6.QtCore import QObject, Signal
from ..core.settings_manager_core import SettingsManagerCore
logger = logging.getLogger(__name__)
class QtSettingsManagerAdapter(QObject):
    """Qt adapter for SettingsManagerCore that maintains backwards compatibility.
    This adapter wraps a SettingsManagerCore instance and provides the exact same
    interface as the original SettingsManager, including Qt signals. All business
    logic is delegated to the pure Python core service.
    Provides:
    - Identical Qt signals to original SettingsManager
    - Same method signatures for complete backwards compatibility  
    - Thread-safe operations via core service
    - Callback-to-signal translation
    """
    # Qt signal matching original SettingsManager exactly
    settings_changed = Signal(str, object)  # Setting key, new value
    def __init__(self, settings_file_path, default_settings=None):
        """Initialize the Qt settings manager adapter.
        Args:
            settings_file_path: Path to the JSON settings file.
            default_settings: Optional dictionary of default settings.
        """
        super().__init__()
        # Create core service instance
        self._core = SettingsManagerCore(settings_file_path, default_settings)
        # Store for backwards compatibility
        self.settings_file_path = settings_file_path
    def get_setting(self, name, default_value=None):
        """Get a setting value by name.
        Args:
            name: Setting name to retrieve.
            default_value: Value to return if setting not found.
        Returns:
            Setting value or default_value if not found.
        """
        return self._core.get_setting(name, default_value)
    def set_setting(self, name, value):
        """Set a setting value with validation.
        This method maintains the exact signature of the original SettingsManager
        for backwards compatibility, handling callback-to-signal translation internally.
        Args:
            name: Setting name to update.
            value: New value for the setting.
        """
        def success_callback(setting_name: str, setting_value: Any) -> None:
            """Handle successful setting update by emitting Qt signal."""
            self.settings_changed.emit(setting_name, setting_value)
        def error_callback(error_message: str) -> None:
            """Handle setting validation/save errors.
            Original SettingsManager logged errors but did not raise exceptions,
            so we maintain that behavior.
            """
            # Error already logged in core service, no additional action needed
            # This maintains the original behavior where set_setting never raised
            pass
        # Delegate to core service with callback translation
        self._core.set_setting(name, value, success_callback, error_callback)
    def get_all_settings(self):
        """Get all current settings.
        Returns:
            Dictionary containing all settings.
        """
        return self._core.get_all_settings()
    # Legacy methods for backwards compatibility
    # These are now handled by the core service, but we maintain the interface
    def _load_settings(self):
        """Load settings from JSON file.
        This method is maintained for backwards compatibility but is now 
        handled automatically by the core service during initialization.
        Calling this method is a no-op since the core service manages loading.
        """
        # Core service handles loading automatically
        # This method exists only for backwards compatibility
        pass
    def _save_settings(self):
        """Save current settings to JSON file.
        This method is maintained for backwards compatibility but is now
        handled automatically by the core service during set_setting operations.
        Calling this method is a no-op since the core service manages saving.
        """
        # Core service handles saving automatically in set_setting
        # This method exists only for backwards compatibility  
        pass
    def _validate_setting(self, name, value):
        """Validate a setting value based on its name.
        This method is maintained for backwards compatibility but validation
        is now handled by the core service. This method delegates to the core
        for consistency.
        Args:
            name: Setting name to validate.
            value: Value to validate.
        Returns:
            True if valid, False otherwise.
        """
        return self._core._validate_setting(name, value)
```

## File: src/abb/services/core/__init__.py
```python
"""Pure Python core service implementations.
This module contains Qt-independent service implementations that use callback-based
communication instead of Qt signals. These services can be used directly in non-Qt
environments or wrapped with Qt adapters for GUI applications.
Services implemented:
- FileServiceCore: File management operations
- PathServiceCore: Path and filename operations  
- SettingsManagerCore: Settings persistence and management
- MetadataHandlerCore: Metadata extraction and manipulation
- ProcessingServiceCore: Audio processing operations
"""
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from .file_service_core import FileServiceCore
    from .metadata_handler_core import MetadataHandlerCore
    from .path_service_core import PathServiceCore
    from .processing_service_core import ProcessingServiceCore
    from .settings_manager_core import SettingsManagerCore
__all__ = [
    "FileServiceCore",
    "PathServiceCore", 
    "SettingsManagerCore",
    "MetadataHandlerCore",
    "ProcessingServiceCore",
]
```

## File: src/abb/services/core/file_service_core.py
```python
"""FileServiceCore - Pure Python file service without Qt dependencies.
This module provides a FileService implementation that uses callback-based communication
instead of Qt signals, enabling pure Python usage and easier testing.
"""
import os
import threading
from pathlib import Path
from typing import Callable, List, Optional, Set
from ..interfaces import ErrorCategory, ServiceError
class FileServiceCore:
    """Pure Python file service implementation.
    Provides file management operations (add, remove, reorder) without Qt dependencies.
    Uses callback-based communication instead of Qt signals for maximum flexibility.
    Thread-safe operations using threading.Lock.
    """
    def __init__(self):
        """Initialize the FileServiceCore with an empty file list."""
        self._files: List[str] = []
        self._valid_extensions: Set[str] = {".mp3", ".m4a", ".m4b", ".aac"}
        self._lock = threading.Lock()
    def get_files(self) -> List[str]:
        """Get the current list of files.
        Returns:
            List of file paths currently managed by the service.
        """
        with self._lock:
            return self._files.copy()
    def get_combined_size(self) -> int:
        """Get total size of all files in bytes.
        Returns:
            Total size in bytes of all managed files.
        """
        with self._lock:
            total = 0
            for file_path in self._files:
                try:
                    total += os.path.getsize(file_path)
                except (OSError, IOError):
                    # Skip files that can't be accessed (deleted, moved, etc.)
                    continue
            return total
    def add_files(self, paths: List[str], 
                  success_callback: Callable[[List[str]], None],
                  error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Add files to the managed list.
        Args:
            paths: List of file paths to add
            success_callback: Called with list of successfully added files
            error_callback: Called with error message if operation fails
        """
        try:
            with self._lock:
                normalized_paths = []
                # Use a set of normalized existing paths for efficient lookup
                existing_paths_lookup = {self._normalize_path(p) for p in self._files}
                for path in paths:
                    try:
                        normalized = self._normalize_path(path)
                        # Skip duplicates
                        if normalized in existing_paths_lookup:
                            continue
                        # Validate file exists and is accessible
                        if not os.path.exists(normalized):
                            if error_callback:
                                error_callback(f"File not found: {path}")
                            continue
                        # Validate file extension
                        if not self._is_valid_audio_file(normalized):
                            if error_callback:
                                error_callback(f"Invalid audio file format: {path}")
                            continue
                        normalized_paths.append(normalized)
                        existing_paths_lookup.add(normalized)
                    except (OSError, IOError) as e:
                        if error_callback:
                            error_callback(f"Error accessing file {path}: {str(e)}")
                        continue
                    except Exception as e:
                        if error_callback:
                            error_callback(f"Unexpected error processing {path}: {str(e)}")
                        continue
                # Add successfully processed files
                if normalized_paths:
                    self._files.extend(normalized_paths)
                # Always call success callback with the files that were added
                success_callback(normalized_paths)
        except Exception as e:
            error_msg = f"Failed to add files: {str(e)}"
            if error_callback:
                error_callback(error_msg)
            else:
                raise ServiceError(error_msg, ErrorCategory.FILE_NOT_FOUND) from None
    def remove_file(self, index: int,
                    success_callback: Callable[[], None],
                    error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Remove file by index.
        Args:
            index: Index of file to remove
            success_callback: Called when file is successfully removed
            error_callback: Called with error message if operation fails
        """
        try:
            with self._lock:
                if index < 0 or index >= len(self._files):
                    error_msg = (f"Invalid index {index}. "
                                f"Must be between 0 and {len(self._files) - 1}")
                    if error_callback:
                        error_callback(error_msg)
                    else:
                        raise ServiceError(error_msg, ErrorCategory.VALIDATION_ERROR)
                    return
                self._files.pop(index)
                success_callback()
        except Exception as e:
            error_msg = f"Failed to remove file at index {index}: {str(e)}"
            if error_callback:
                error_callback(error_msg)
            else:
                raise ServiceError(error_msg, ErrorCategory.PROCESSING_ERROR) from None
    def reorder_files(self, new_order: List[str],
                      success_callback: Callable[[], None],
                      error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Reorder files according to new path order.
        Args:
            new_order: List of file paths in desired order
            success_callback: Called when reordering is complete
            error_callback: Called with error message if operation fails
        """
        try:
            with self._lock:
                # Validate that new_order contains the same files as current list
                current_normalized = {self._normalize_path(p) for p in self._files}
                new_normalized = {self._normalize_path(p) for p in new_order}
                if current_normalized != new_normalized:
                    error_msg = "New order must contain exactly the same files as current list"
                    if error_callback:
                        error_callback(error_msg)
                    else:
                        raise ServiceError(error_msg, ErrorCategory.VALIDATION_ERROR)
                    return
                # Normalize all paths in the new order
                normalized_new_order = [self._normalize_path(p) for p in new_order]
                self._files = normalized_new_order
                success_callback()
        except Exception as e:
            error_msg = f"Failed to reorder files: {str(e)}"
            if error_callback:
                error_callback(error_msg)
            else:
                raise ServiceError(error_msg, ErrorCategory.PROCESSING_ERROR) from None
    def _normalize_path(self, path: str) -> str:
        """Normalize a file path to its absolute, canonical form.
        Args:
            path: File path to normalize
        Returns:
            Normalized path
        Raises:
            ServiceError: If path cannot be normalized
        """
        try:
            return str(Path(path).resolve())
        except (OSError, ValueError) as e:
            raise ServiceError(f"Cannot normalize path '{path}': {str(e)}", 
                             ErrorCategory.VALIDATION_ERROR) from e
    def _is_valid_audio_file(self, path: str) -> bool:
        """Check if a file has a valid audio extension.
        Args:
            path: File path to check
        Returns:
            True if the file has a valid audio extension, False otherwise
        """
        try:
            ext = Path(path).suffix.lower()
            return ext in self._valid_extensions
        except Exception:
            return False
    def clear_files(self, 
                    success_callback: Callable[[], None],
                    error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Clear all files from the managed list.
        Args:
            success_callback: Called when files are cleared
            error_callback: Called with error message if operation fails
        """
        try:
            with self._lock:
                self._files.clear()
                success_callback()
        except Exception as e:
            error_msg = f"Failed to clear files: {str(e)}"
            if error_callback:
                error_callback(error_msg)
            else:
                raise ServiceError(error_msg, ErrorCategory.PROCESSING_ERROR) from None
    def get_file_count(self) -> int:
        """Get the number of files currently managed.
        Returns:
            Number of files in the managed list.
        """
        with self._lock:
            return len(self._files)
    def format_combined_size(self) -> str:
        """Get the combined size formatted for display.
        Returns:
            Human-readable size string (e.g., "1.5 MB", "2.3 GB").
        """
        size = self.get_combined_size()
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.2f} GB"
    def validate_files(self, 
                       success_callback: Callable[[List[str]], None],
                       error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Validate that all managed files still exist and are accessible.
        Removes any files that are no longer accessible.
        Args:
            success_callback: Called with list of files that were removed (if any)
            error_callback: Called with error message if validation fails
        """
        try:
            with self._lock:
                removed_files = []
                valid_files = []
                for file_path in self._files:
                    try:
                        if os.path.exists(file_path) and os.access(file_path, os.R_OK):
                            valid_files.append(file_path)
                        else:
                            removed_files.append(file_path)
                    except (OSError, IOError):
                        removed_files.append(file_path)
                if removed_files:
                    self._files = valid_files
                success_callback(removed_files)
        except Exception as e:
            error_msg = f"Failed to validate files: {str(e)}"
            if error_callback:
                error_callback(error_msg)
            else:
                raise ServiceError(error_msg, ErrorCategory.PROCESSING_ERROR) from None
```

## File: src/abb/services/core/metadata_handler_core.py
```python
"""MetadataHandlerCore - Pure Python metadata handler without Qt dependencies.
This is the core implementation of the IMetadataHandler protocol that provides
callback-based communication instead of Qt signals. It wraps existing metadata
utilities from metadata_utils while maintaining complete Qt independence.
Key features:
- Implements IMetadataHandler protocol
- Thread-safe operations using threading.Lock
- Wraps metadata_utils functions for compatibility
- Maintains ABB to FFmpeg metadata mapping as single source of truth
- Supports cover art handling without Qt pixmaps
- Error handling with proper error categories
"""
import os
import threading
from pathlib import Path
from typing import Any, Callable, Dict, Optional
# Import core metadata functions (avoiding Qt dependencies)
from ...metadata_utils import (
    apply_metadata_defaults,
    clear_metadata_cache,
    extract_cover,
    extract_metadata,
)
from ..interfaces import ErrorCategory, ServiceError
class MetadataHandlerCore:
    """Pure Python metadata handler implementing IMetadataHandler protocol.
    This class provides a Qt-independent implementation of metadata operations
    using callback-based communication. It wraps existing metadata_utils functions
    while providing thread-safe state management and proper error handling.
    """
    # Single source of truth for ABB to FFmpeg metadata mapping
    # Inherited from UnifiedMetadataHandler for consistency
    ABB_TO_FFMPEG_METADATA_MAP_GENERAL = {
        "title": "title",
        "album": "album", 
        "year": "date",
        "genre": "genre",
        "narrator": "composer",
        "series": "mood",
        "series_pos": "track",
        "copyright": "copyright",
        "sort_title": "title_sort",
        "sort_artist": "artist_sort",
        "sort_album_artist": "album_artist_sort",
        "sort_composer": "composer_sort",
        "disc_number": "disc",
        "compilation": "compilation",
        "grouping": "grouping",
        "lyrics": "lyrics",
        "publisher": "publisher",
        "comment": "comment",
        "description": "description"
    }
    def __init__(self):
        """Initialize the metadata handler with empty state."""
        self._lock = threading.Lock()
        self._current_metadata: Dict[str, Any] = {}
        self._current_file_path: Optional[str] = None
        self._cover_art_path: Optional[str] = None
        self._cover_art_data: Optional[bytes] = None
    def get_current_metadata(self) -> Dict[str, Any]:
        """Get current metadata state.
        Thread-safe access to current metadata dictionary.
        Returns:
            Dictionary containing current metadata.
        """
        with self._lock:
            return self._current_metadata.copy()
    def load_from_file(self, path: str,
                       success_callback: Callable[[Dict[str, Any]], None],
                       error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Load metadata from audio file.
        Args:
            path: Path to audio file
            success_callback: Called with extracted metadata
            error_callback: Called with error message if operation fails
        """
        def _load_operation():
            try:
                # Validate file exists
                if not os.path.exists(path):
                    error_msg = f"File not found: {path}"
                    if error_callback:
                        error_callback(error_msg)
                    return
                # Extract metadata using existing utilities
                metadata = self._extract_metadata_safe(path)
                with self._lock:
                    self._current_metadata = metadata.copy()
                    self._current_file_path = path
                    # Handle cover art extraction
                    self._extract_cover_art_safe(path)
                # Call success callback with metadata
                success_callback(metadata)
            except Exception as e:
                error_msg = f"Failed to load metadata from {path}: {str(e)}"
                if error_callback:
                    error_callback(error_msg)
        # Execute in thread for non-blocking operation
        thread = threading.Thread(target=_load_operation)
        thread.daemon = True
        thread.start()
    def update_field(self, field: str, value: Any,
                     success_callback: Callable[[Dict[str, Any]], None],
                     error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Update a metadata field.
        Args:
            field: Metadata field name
            value: New value for the field
            success_callback: Called with updated metadata
            error_callback: Called with error message if operation fails
        """
        try:
            with self._lock:
                self._current_metadata[field] = value
                updated_metadata = self._current_metadata.copy()
            success_callback(updated_metadata)
        except Exception as e:
            error_msg = f"Failed to update field '{field}': {str(e)}"
            if error_callback:
                error_callback(error_msg)
    def get_for_ffmpeg(self) -> Dict[str, str]:
        """Get metadata formatted for FFmpeg.
        Uses the ABB_TO_FFMPEG_METADATA_MAP_GENERAL mapping to transform
        metadata fields into FFmpeg-compatible format.
        Returns:
            Dictionary with FFmpeg-compatible metadata mappings.
        """
        with self._lock:
            ffmpeg_metadata = {}
            # Use the unified mapping - single source of truth
            for abb_key, ffmpeg_key in self.ABB_TO_FFMPEG_METADATA_MAP_GENERAL.items():
                value = self._current_metadata.get(abb_key)
                if value is not None and str(value).strip() != "":
                    ffmpeg_metadata[ffmpeg_key] = str(value)
            # Handle special mappings (from legacy command_builder.py logic)
            artist_val = self._current_metadata.get('artist')
            if artist_val:
                ffmpeg_metadata['artist'] = str(artist_val)
                # Set album_artist to artist if not explicitly set
                if 'album_artist' not in ffmpeg_metadata:
                    ffmpeg_metadata['album_artist'] = str(artist_val)
            # Handle comment/description priority
            comment_val = self._current_metadata.get('comment')
            description_val = self._current_metadata.get('description')
            if comment_val:
                ffmpeg_metadata['comment'] = str(comment_val)
            elif description_val:
                ffmpeg_metadata['comment'] = str(description_val)
            # Handle series_sort priority for album_sort
            series_sort_val = self._current_metadata.get('series_sort')
            sort_album_val = self._current_metadata.get('sort_album')
            if series_sort_val:
                ffmpeg_metadata['album_sort'] = str(series_sort_val)
            elif sort_album_val:
                ffmpeg_metadata['album_sort'] = str(sort_album_val)
            return ffmpeg_metadata
    def clear_metadata(self,
                       success_callback: Callable[[], None],
                       error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Clear all metadata.
        Args:
            success_callback: Called when metadata is cleared
            error_callback: Called with error message if operation fails
        """
        try:
            with self._lock:
                self._current_metadata.clear()
                self._current_file_path = None
                self._cover_art_path = None
                self._cover_art_data = None
            # Clear the global metadata cache as well
            clear_metadata_cache()
            success_callback()
        except Exception as e:
            error_msg = f"Failed to clear metadata: {str(e)}"
            if error_callback:
                error_callback(error_msg)
    def set_cover_art(self, path: str,
                      success_callback: Callable[[Optional[str]], None],
                      error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Set cover art from file path.
        Args:
            path: Path to cover art image file
            success_callback: Called with cover art path (None if cleared)
            error_callback: Called with error message if operation fails
        """
        def _set_cover_operation():
            try:
                if not path:  # Clear cover art
                    with self._lock:
                        self._cover_art_path = None
                        self._cover_art_data = None
                    success_callback(None)
                    return
                # Validate file exists and is an image
                if not os.path.exists(path):
                    error_msg = f"Cover art file not found: {path}"
                    if error_callback:
                        error_callback(error_msg)
                    return
                # Check if it's a supported image format
                valid_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
                file_ext = Path(path).suffix.lower()
                if file_ext not in valid_extensions:
                    error_msg = f"Unsupported cover art format: {file_ext}"
                    if error_callback:
                        error_callback(error_msg)
                    return
                # Read image data for validation
                try:
                    with open(path, 'rb') as f:
                        cover_data = f.read()
                    with self._lock:
                        self._cover_art_path = path
                        self._cover_art_data = cover_data
                    success_callback(path)
                except Exception as e:
                    error_msg = f"Failed to read cover art file: {str(e)}"
                    if error_callback:
                        error_callback(error_msg)
            except Exception as e:
                error_msg = f"Failed to set cover art: {str(e)}"
                if error_callback:
                    error_callback(error_msg)
        # Execute in thread for non-blocking operation
        thread = threading.Thread(target=_set_cover_operation)
        thread.daemon = True
        thread.start()
    def get_cover_art_path(self) -> Optional[str]:
        """Get current cover art path.
        Returns:
            Path to current cover art file, or None if no cover art set.
        """
        with self._lock:
            return self._cover_art_path
    # Additional utility methods to support the interface
    def has_metadata(self) -> bool:
        """Check if metadata is currently loaded.
        Returns:
            True if metadata is loaded, False otherwise.
        """
        with self._lock:
            return bool(self._current_metadata)
    def get_field(self, field: str, default: Any = None) -> Any:
        """Get a specific metadata field value.
        Args:
            field: The field name to retrieve
            default: Default value if field not found
        Returns:
            The field value or default
        """
        with self._lock:
            return self._current_metadata.get(field, default)
    def reset_state(self) -> None:
        """Reset the handler's internal state.
        Thread-safe reset of all internal state.
        """
        with self._lock:
            self._current_metadata.clear()
            self._current_file_path = None
            self._cover_art_path = None
            self._cover_art_data = None
    def get_current_file_path(self) -> Optional[str]:
        """Get the file path of currently loaded metadata.
        Returns:
            Current file path or None if no file loaded
        """
        with self._lock:
            return self._current_file_path
    def get_cover_art_data(self) -> Optional[bytes]:
        """Get cover art data as bytes.
        Returns:
            Cover art data as bytes or None if no cover art set.
        """
        with self._lock:
            return self._cover_art_data
    # Private helper methods
    def _extract_metadata_safe(self, file_path: str) -> Dict[str, Any]:
        """Safely extract metadata from file using existing utilities.
        Args:
            file_path: Path to audio file
        Returns:
            Dictionary containing extracted metadata
        Raises:
            ServiceError: If metadata extraction fails
        """
        try:
            # Use existing extract_metadata function
            metadata = extract_metadata(file_path)
            # Apply defaults using existing function
            metadata = apply_metadata_defaults(metadata)
            # Remove Qt-dependent cover_art field if present
            if 'cover_art' in metadata:
                del metadata['cover_art']
            return metadata
        except Exception as e:
            raise ServiceError(
                f"Failed to extract metadata from {file_path}: {str(e)}",
                ErrorCategory.METADATA_ERROR
            ) from e
    def _extract_cover_art_safe(self, file_path: str) -> None:
        """Safely extract cover art from file.
        Args:
            file_path: Path to audio file
        """
        try:
            # Use existing extract_cover function to get raw bytes
            cover_data = extract_cover(file_path)
            if cover_data:
                self._cover_art_data = cover_data
                # Store original file path as cover art source
                self._cover_art_path = file_path
            else:
                self._cover_art_data = None
                self._cover_art_path = None
        except Exception as e:
            # Don't raise error for cover art extraction failure
            # Just log and continue without cover art
            print(f"Warning: Failed to extract cover art from {file_path}: {e}")
            self._cover_art_data = None
            self._cover_art_path = None
    @classmethod
    def get_metadata_mapping(cls) -> Dict[str, str]:
        """Get the ABB to FFmpeg metadata mapping.
        Provides access to the single source of truth mapping without
        requiring an instance of the handler.
        Returns:
            Dictionary mapping ABB field names to FFmpeg field names
        """
        return cls.ABB_TO_FFMPEG_METADATA_MAP_GENERAL.copy()
# Factory function for creating instances
def create_metadata_handler_core() -> MetadataHandlerCore:
    """Create a new MetadataHandlerCore instance.
    Returns:
        New handler instance
    """
    return MetadataHandlerCore()
# Feature flag support
def is_core_metadata_enabled() -> bool:
    """Check if the core metadata handler should be used.
    Reads the ABB_PURE_SERVICES environment variable.
    Returns:
        True if ABB_PURE_SERVICES is set to 'true' (case-insensitive), False otherwise
    """
    return os.environ.get('ABB_PURE_SERVICES', 'False').lower() == 'true'
```

## File: src/abb/services/core/path_service_core.py
```python
"""Pure Python PathService implementation without Qt dependencies.
This module provides PathServiceCore, a pure Python implementation of the IPathService protocol.
It handles output path calculation and filename generation for audiobook processing without
any Qt dependencies, making it easier to test and reuse in different contexts.
"""
from pathlib import Path
from typing import Any, Dict
class PathServiceCore:
    """Pure Python implementation of path and filename generation service.
    This service provides functionality for:
    - Calculating output directory paths with optional subdirectory patterns
    - Generating output filenames using different naming patterns
    - Path and filename sanitization for cross-platform compatibility
    The service is stateless and thread-safe, implementing the IPathService protocol.
    """
    def calculate_output_path(self, base_dir: str, metadata: Dict[str, Any], 
                            use_subdirectory_pattern: bool) -> str:
        """Calculate the output directory path based on settings and metadata.
        Creates subdirectories based on artist and series metadata when enabled.
        The subdirectory structure is: base_dir/artist/series (if both present).
        Args:
            base_dir: Base output directory path.
            metadata: Dictionary containing audio metadata (artist, series).
            use_subdirectory_pattern: Whether to create subdirectories for author/series.
        Returns:
            Full output directory path as string.
        Example:
            >>> service = PathServiceCore()
            >>> metadata = {"artist": "John Doe", "series": "Mystery Series"}
            >>> service.calculate_output_path("/output", metadata, True)
            "/output/John Doe/Mystery Series"
        """
        output_path = Path(base_dir)
        if use_subdirectory_pattern:
            author = self._safe_str(metadata.get("artist", "")).strip()
            if author:
                output_path = output_path / self._sanitize_path(author)
            series = self._safe_str(metadata.get("series", "")).strip()
            if series:
                output_path = output_path / self._sanitize_path(series)
        return str(output_path)
    def generate_output_filename(self, metadata: Dict[str, Any], pattern_id: int) -> str:
        """Generate output filename based on pattern and metadata.
        Supports three filename patterns:
        - Pattern 0: "Title (Year)" - title with optional year in parentheses
        - Pattern 1: "Author - Title" - author and title separated by dash
        - Pattern 2: "Series - Title" - series and title separated by dash
        Args:
            metadata: Dictionary containing audio metadata (title, artist, series, year).
            pattern_id: Pattern to use (0=Title (Year), 1=Author - Title, 2=Series - Title).
        Returns:
            Sanitized filename string with .m4b extension.
        Example:
            >>> service = PathServiceCore()
            >>> metadata = {"title": "The Book", "artist": "Author", "year": "2023"}
            >>> service.generate_output_filename(metadata, 0)
            "The Book (2023).m4b"
        """
        filename = "audiobook.m4b"  # Default fallback
        if pattern_id == 0:  # Title (Year)
            title = self._safe_str(metadata.get("title", ""))
            year = self._safe_str(metadata.get("year", ""))
            if title.strip():
                filename = f"{title} ({year}).m4b" if year.strip() else f"{title}.m4b"
        elif pattern_id == 1:  # Author - Title
            author = self._safe_str(metadata.get("artist", ""))
            title = self._safe_str(metadata.get("title", ""))
            if author.strip() and title.strip():
                filename = f"{author} - {title}.m4b"
            elif title.strip():
                filename = f"{title}.m4b"
        elif pattern_id == 2:  # Series - Title
            series = self._safe_str(metadata.get("series", ""))
            title = self._safe_str(metadata.get("title", ""))
            if series.strip() and title.strip():
                filename = f"{series} - {title}.m4b"
            elif title.strip():
                filename = f"{title}.m4b"
        # Validate generated filename to prevent empty or invalid names
        if not filename.strip() or filename.strip() in [".m4b", "().m4b", "( ).m4b"]:
            filename = "audiobook.m4b"
        return self._sanitize_filename(filename)
    def _sanitize_path(self, path_str: str) -> str:
        r"""Remove invalid characters from path components.
        Replaces characters that are invalid in file/directory names on most
        operating systems with underscores to ensure cross-platform compatibility.
        Args:
            path_str: Path string to sanitize.
        Returns:
            Sanitized path string with invalid characters replaced by underscores.
        Note:
            The following characters are considered invalid: < > : " / \ | ? *
        """
        invalid_chars = ["<", ">", ":", '"', "/", "\\", "|", "?", "*"]
        for char in invalid_chars:
            path_str = path_str.replace(char, "_")
        return path_str
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for filesystem compatibility.
        Uses the same sanitization rules as path components to ensure
        the filename is valid across different operating systems.
        Args:
            filename: Filename to sanitize.
        Returns:
            Sanitized filename with invalid characters replaced.
        """
        return self._sanitize_path(filename)
    def _safe_str(self, val: Any) -> str:
        """Safely convert value to string.
        Handles None values gracefully by converting them to empty strings,
        while converting all other values to their string representation.
        Args:
            val: Value to convert (can be None, int, str, etc.).
        Returns:
            String representation of value or empty string if None.
        """
        return str(val) if val is not None else ""
```

## File: src/abb/services/core/processing_service_core.py
```python
"""ProcessingServiceCore - Pure Python processing service without Qt dependencies.
Implements the IProcessingService protocol using callback-based communication,
Python threading, and subprocess instead of Qt signals, QThread, and QProcess.
"""
import os
import re
import subprocess
import threading
import time
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional
from ...ffmpeg_utils import (
    _get_executable_path,
    build_ffmpeg_command,
    build_ffmpeg_preview_command,
)
from ...metadata_utils import get_duration
from ...processing_validator import ProcessingValidator
from ..interfaces import IPathService
class ProcessingServiceCore:
    """Pure Python processing service without Qt dependencies.
    Implements IProcessingService protocol using callback-based communication
    instead of Qt signals, and Python threading/subprocess instead of Qt equivalents.
    """
    def __init__(self, path_service: IPathService):
        """Initialize the ProcessingServiceCore.
        Args:
            path_service: Path service dependency for output path calculations
        """
        self._path_service = path_service
        self._validator = ProcessingValidator()
        # Threading state
        self._lock = threading.Lock()
        self._is_processing = False
        self._cancel_event = threading.Event()
        self._process: Optional[subprocess.Popen] = None
        self._processing_thread: Optional[threading.Thread] = None
        self._progress_thread: Optional[threading.Thread] = None
        # Processing state
        self._temp_files: List[str] = []
        self._output_file: Optional[str] = None
        self._total_duration_seconds = 0.0
        # Callbacks for current operation
        self._progress_callback: Optional[Callable[[int], None]] = None
        self._status_callback: Optional[Callable[[str], None]] = None
        self._complete_callback: Optional[Callable[[str], None]] = None
        self._error_callback: Optional[Callable[[str], None]] = None
        # Get FFmpeg path once
        self._ffmpeg_path = self._get_ffmpeg_path()
    def process(self, 
                input_files: List[str],
                output_path: str,
                output_filename: str,
                metadata: Dict[str, Any],
                settings: Dict[str, Any],
                progress_callback: Callable[[int], None],
                status_callback: Callable[[str], None],
                complete_callback: Callable[[str], None],
                error_callback: Callable[[str], None]) -> None:
        """Process audio files into audiobook format.
        Args:
            input_files: List of input audio file paths
            output_path: Directory for output file
            output_filename: Name of output file
            metadata: Metadata to embed in output
            settings: Processing settings (bitrate, format, etc.)
            progress_callback: Called with progress percentage (0-100)
            status_callback: Called with status messages
            complete_callback: Called with output file path when complete
            error_callback: Called with error message if processing fails
        """
        with self._lock:
            if self._is_processing:
                error_callback("Processing already in progress")
                return
            self._is_processing = True
            self._cancel_event.clear()
        # Store callbacks
        self._progress_callback = progress_callback
        self._status_callback = status_callback
        self._complete_callback = complete_callback
        self._error_callback = error_callback
        # Start processing in background thread
        self._processing_thread = threading.Thread(
            target=self._process_impl,
            args=(input_files, output_path, output_filename, metadata, settings),
            daemon=True
        )
        self._processing_thread.start()
    def process_preview(self,
                        input_file: str,
                        metadata: Dict[str, Any],
                        settings: Dict[str, Any],
                        duration_seconds: int,
                        complete_callback: Callable[[str], None],
                        error_callback: Callable[[str], None]) -> None:
        """Generate a preview of the processed audio.
        Args:
            input_file: Path to input audio file
            metadata: Metadata to embed
            settings: Processing settings
            duration_seconds: Length of preview in seconds
            complete_callback: Called with preview file path
            error_callback: Called with error message if operation fails
        """
        with self._lock:
            if self._is_processing:
                error_callback("Processing already in progress")
                return
            self._is_processing = True
            self._cancel_event.clear()
        # Store callbacks (no progress for preview)
        self._progress_callback = None
        self._status_callback = lambda msg: None  # No-op for preview
        self._complete_callback = complete_callback
        self._error_callback = error_callback
        # Start preview processing in background thread
        self._processing_thread = threading.Thread(
            target=self._process_preview_impl,
            args=(input_file, metadata, settings, duration_seconds),
            daemon=True
        )
        self._processing_thread.start()
    def cancel(self,
               success_callback: Callable[[], None],
               error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Cancel current processing operation.
        Args:
            success_callback: Called when cancellation is complete
            error_callback: Called with error message if cancellation fails
        """
        with self._lock:
            if not self._is_processing:
                success_callback()
                return
        # Set cancel event
        self._cancel_event.set()
        # Start cancellation in background thread to avoid blocking
        cancel_thread = threading.Thread(
            target=self._cancel_impl,
            args=(success_callback, error_callback),
            daemon=True
        )
        cancel_thread.start()
    def is_processing(self) -> bool:
        """Check if processing is currently active.
        Returns:
            True if processing is active, False otherwise.
        """
        with self._lock:
            return self._is_processing
    def _process_impl(self,
                      input_files: List[str],
                      output_path: str,
                      output_filename: str,
                      metadata: Dict[str, Any],
                      settings: Dict[str, Any]) -> None:
        """Implementation of full processing in background thread."""
        try:
            self._temp_files = []
            # Validate FFmpeg
            if not self._ffmpeg_path or not os.path.isfile(self._ffmpeg_path):
                self._emit_error("FFmpeg executable not found or not executable")
                return
            # Calculate total duration
            self._total_duration_seconds = sum(get_duration(file_path) for file_path in input_files)
            # Validate inputs
            input_paths = [Path(f) for f in input_files]
            bitrate_kbps = settings.get('bitrate', 64)
            estimated_size_mb = (
                self._total_duration_seconds * bitrate_kbps * 1000 / 8
            ) / (1024 * 1024)
            valid, error_msg = self._validator.validate_all(
                input_files=input_paths,
                output_directory=Path(output_path),
                estimated_size_mb=estimated_size_mb
            )
            if not valid:
                self._emit_error(f"Validation failed: {error_msg}")
                return
            # Handle cover art
            cover_art_temp_path = metadata.get("cover_art_temp_path")
            if cover_art_temp_path and os.path.exists(cover_art_temp_path):
                self._temp_files.append(cover_art_temp_path)
            # Create output directory
            os.makedirs(output_path, exist_ok=True)
            self._output_file = os.path.join(output_path, output_filename)
            # Build FFmpeg command
            ffprobe_exe_path = _get_executable_path("ffprobe")
            cmd = build_ffmpeg_command(
                input_files=input_files,
                output_file_full_path=self._output_file,
                metadata=metadata,
                settings=settings,
                ffmpeg_exe_path=self._ffmpeg_path,
                ffprobe_exe_path=ffprobe_exe_path
            )
            if not cmd:
                self._emit_error("Failed to build FFmpeg command. Check input files and settings.")
                return
            # Run FFmpeg process
            self._emit_status("Starting audio processing...")
            self._run_ffmpeg_process(cmd)
        except Exception as e:
            self._emit_error(f"Processing error: {str(e)}")
        finally:
            self._cleanup_processing()
    def _process_preview_impl(self,
                              input_file: str,
                              metadata: Dict[str, Any],
                              settings: Dict[str, Any],
                              duration_seconds: int) -> None:
        """Implementation of preview processing in background thread."""
        try:
            self._temp_files = []
            self._total_duration_seconds = duration_seconds
            # Build preview command
            cmd = build_ffmpeg_preview_command(
                input_file_path=input_file,
                metadata=metadata,
                settings=settings,
                temp_cover_path=metadata.get("cover_art_temp_path"),
                duration_seconds=duration_seconds
            )
            if not cmd:
                self._emit_error("Failed to build FFmpeg preview command")
                return
            # Output file is last argument
            self._output_file = cmd[-1]
            self._temp_files.append(self._output_file)  # Preview output is temporary
            # Run FFmpeg process
            self._emit_status("Generating preview...")
            self._run_ffmpeg_process(cmd)
        except Exception as e:
            self._emit_error(f"Preview processing error: {str(e)}")
        finally:
            self._cleanup_processing()
    def _run_ffmpeg_process(self, cmd: List[str]) -> None:
        """Run FFmpeg process and monitor progress."""
        try:
            # Start process
            self._process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1
            )
            # Start progress monitoring thread
            if self._progress_callback:
                self._progress_thread = threading.Thread(
                    target=self._monitor_progress,
                    daemon=True
                )
                self._progress_thread.start()
            # Wait for process completion
            stdout, stderr = self._process.communicate()
            # Check for cancellation
            if self._cancel_event.is_set():
                self._cleanup_after_cancel()
                return
            # Check exit code
            if self._process.returncode == 0:
                # Success
                if self._progress_callback:
                    self._progress_callback(100)
                self._emit_status("Processing completed successfully")
                self._cleanup_temp_files_only()
                if self._complete_callback:
                    self._complete_callback(self._output_file or "")
            else:
                # Error
                error_msg = f"FFmpeg failed (exit code {self._process.returncode})"
                if stderr.strip():
                    error_msg += f": {stderr.strip()}"
                self._emit_error(error_msg)
        except Exception as e:
            self._emit_error(f"Error running FFmpeg process: {str(e)}")
    def _monitor_progress(self) -> None:
        """Monitor FFmpeg stderr output for progress updates."""
        if not self._process or not self._progress_callback:
            return
        try:
            while self._process.poll() is None and not self._cancel_event.is_set():
                if self._process.stderr:
                    line = self._process.stderr.readline()
                    if line:
                        line = line.strip()
                        # Parse progress
                        progress = self._parse_ffmpeg_progress(line)
                        if progress is not None:
                            self._progress_callback(progress)
                        # Emit status for all lines
                        if self._status_callback:
                            self._status_callback(line)
                # Brief sleep to avoid busy waiting
                time.sleep(0.1)
        except Exception as e:
            if self._status_callback:
                self._status_callback(f"Progress monitoring error: {str(e)}")
    def _parse_ffmpeg_progress(self, stderr_line: str) -> Optional[int]:
        """Parse FFmpeg stderr output to extract progress information.
        Args:
            stderr_line: FFmpeg stderr output line
        Returns:
            Progress percentage (0-100) or None if no progress info found
        """
        match = re.search(r"time=(\d{2}):(\d{2}):(\d{2})\.(\d{2})", stderr_line)
        if match:
            hours, minutes, seconds = int(match.group(1)), int(match.group(2)), int(match.group(3))
            current_time_seconds = (hours * 3600) + (minutes * 60) + seconds
            if self._total_duration_seconds > 0:
                percentage = (current_time_seconds / self._total_duration_seconds) * 100
                # Clamp to 0-100
                return max(0, min(100, int(percentage)))
        return None
    def _cancel_impl(self,
                     success_callback: Callable[[], None],
                     error_callback: Optional[Callable[[str], None]]) -> None:
        """Implementation of cancellation in background thread."""
        try:
            # Terminate process if running
            if self._process:
                self._process.terminate()
                # Wait for graceful termination
                try:
                    self._process.wait(timeout=2.0)
                except subprocess.TimeoutExpired:
                    # Force kill if not terminated
                    self._process.kill()
                    self._process.wait(timeout=1.0)
            # Wait for threads to finish
            if self._processing_thread and self._processing_thread.is_alive():
                self._processing_thread.join(timeout=3.0)
            if self._progress_thread and self._progress_thread.is_alive():
                self._progress_thread.join(timeout=1.0)
            # Cleanup
            self._cleanup_after_cancel()
            success_callback()
        except Exception as e:
            if error_callback:
                error_callback(f"Cancellation error: {str(e)}")
            else:
                # Fallback to success if no error callback
                success_callback()
    def _cleanup_processing(self) -> None:
        """Clean up after processing completion."""
        with self._lock:
            self._is_processing = False
        self._process = None
        self._processing_thread = None
        self._progress_thread = None
        # Clear callbacks
        self._progress_callback = None
        self._status_callback = None
        self._complete_callback = None
        self._error_callback = None
    def _cleanup_temp_files_only(self) -> None:
        """Clean up temporary files but preserve output file."""
        temp_files_to_remove = []
        for temp_file in self._temp_files:
            # Don't remove the output file for successful processing
            if temp_file != self._output_file:
                temp_files_to_remove.append(temp_file)
        for temp_file in temp_files_to_remove:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except OSError:
                    pass  # Ignore errors
        # Only remove non-output files from the list
        self._temp_files = [f for f in self._temp_files if f == self._output_file]
    def _cleanup_after_cancel(self) -> None:
        """Clean up after cancellation or error."""
        # Clean up all temp files including output
        for temp_file in self._temp_files:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except OSError:
                    pass
        # Clean up output file if it exists
        if self._output_file and os.path.exists(self._output_file):
            try:
                os.remove(self._output_file)
            except OSError:
                pass
        self._temp_files = []
        self._output_file = None
    def _emit_status(self, message: str) -> None:
        """Emit status message via callback."""
        if self._status_callback:
            self._status_callback(message)
    def _emit_error(self, message: str) -> None:
        """Emit error message via callback and cleanup."""
        self._cleanup_after_cancel()
        if self._error_callback:
            self._error_callback(message)
    def _get_ffmpeg_path(self) -> str:
        """Get the path to the FFmpeg executable.
        Returns:
            Path to the FFmpeg executable
        """
        ffmpeg_path = _get_executable_path("ffmpeg")
        return str(ffmpeg_path) if ffmpeg_path else "ffmpeg"
```

## File: src/abb/services/core/settings_manager_core.py
```python
"""Pure Python settings manager without Qt dependencies."""
import json
import logging
import os
import threading
from typing import Any, Callable, Dict, Optional
logger = logging.getLogger(__name__)
class SettingsManagerCore:
    """Pure Python settings manager implementing ISettingsManager protocol.
    This class provides the same functionality as SettingsManager but without Qt dependencies.
    It uses callback-based communication instead of Qt signals and is thread-safe.
    """
    def __init__(self, settings_file_path: str, default_settings: Optional[Dict[str, Any]] = None):
        """Initialize the settings manager.
        Args:
            settings_file_path: Path to the JSON settings file.
            default_settings: Optional dictionary of default settings.
        """
        self.settings_file_path = settings_file_path
        self._settings: Dict[str, Any] = {}
        self._lock = threading.Lock()
        if default_settings:
            self._settings.update(default_settings)
        self._load_settings()
    def _load_settings(self) -> None:
        """Load settings from JSON file, merging with existing defaults."""
        with self._lock:
            if os.path.exists(self.settings_file_path):
                try:
                    with open(self.settings_file_path, 'r') as f:
                        loaded_settings = json.load(f)
                        self._settings.update(loaded_settings)
                except FileNotFoundError:
                    logger.warning(
                        "Settings file not found: %s. Using default settings.",
                        self.settings_file_path
                    )
                except json.JSONDecodeError:
                    logger.warning(
                        f"Corrupt settings file: {self.settings_file_path}. Using default settings."
                    )
                except IOError as e:
                    logger.error(f"Error reading settings file {self.settings_file_path}: {e}")
    def _save_settings(self) -> None:
        """Save current settings to JSON file."""
        os.makedirs(os.path.dirname(self.settings_file_path), exist_ok=True)
        try:
            with open(self.settings_file_path, 'w') as f:
                json.dump(self._settings, f, indent=4)
        except IOError as e:
            logger.error(f"Error writing settings to file {self.settings_file_path}: {e}")
    def _validate_setting(self, name: str, value: Any) -> bool:
        """Validate a setting value based on its name.
        Args:
            name: Setting name to validate.
            value: Value to validate.
        Returns:
            True if valid, False otherwise.
        """
        if name == "output_bitrate":
            valid_bitrates = [32, 48, 56, 64, 96, 128]
            if not isinstance(value, int) or value not in valid_bitrates:
                logger.warning(f"Invalid output_bitrate: {value}. Must be one of {valid_bitrates}.")
                return False
        elif name == "output_directory":
            if not isinstance(value, str) or not value:
                logger.warning(f"Invalid output_directory: {value}. Must be a non-empty string.")
                return False
        elif name == "output_filename_pattern":
            if not isinstance(value, int) or value not in [0, 1, 2]:
                logger.warning(f"Invalid output_filename_pattern: {value}. Must be 0, 1, or 2.")
                return False
        return True
    def get_setting(self, name: str, default: Any = None) -> Any:
        """Get setting value by name.
        Args:
            name: Setting name
            default: Default value if setting not found
        Returns:
            Setting value or default
        """
        with self._lock:
            return self._settings.get(name, default)
    def set_setting(self, name: str, value: Any,
                    success_callback: Callable[[str, Any], None],
                    error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Set setting value.
        Args:
            name: Setting name
            value: New setting value
            success_callback: Called with setting name and value when saved
            error_callback: Called with error message if operation fails
        """
        try:
            with self._lock:
                if self._validate_setting(name, value):
                    self._settings[name] = value
                    self._save_settings()
                    # Execute callback outside the lock to prevent deadlocks
            # Call success callback outside lock
            if self._validate_setting(name, value):
                success_callback(name, value)
            else:
                error_message = (f"Validation failed for setting '{name}' "
                                f"with value '{value}'. Setting not updated.")
                logger.error(error_message)
                if error_callback:
                    error_callback(error_message)
        except Exception as e:
            error_message = f"Error setting '{name}' to '{value}': {str(e)}"
            logger.error(error_message)
            if error_callback:
                error_callback(error_message)
    def get_all_settings(self) -> Dict[str, Any]:
        """Get all current settings.
        Returns:
            Dictionary containing all settings.
        """
        with self._lock:
            return self._settings.copy()
```

## File: src/abb/services/__init__.py
```python
"""ABB Services module."""
```

## File: src/abb/services/interfaces.py
```python
"""Pure Python Protocol interfaces for ABB services.
This module defines Protocol interfaces that enable service decoupling from Qt dependencies.
Services implementing these protocols can use callback-based communication instead of Qt signals,
allowing for pure Python implementations that are easier to test and reuse.
Feature flag: ABB_PURE_SERVICES controls whether these interfaces are used.
"""
from typing import Any, Callable, Dict, List, Optional, Protocol
class IFileService(Protocol):
    """Protocol for file management operations."""
    def get_files(self) -> List[str]:
        """Get current list of files.
        Returns:
            List of file paths currently managed by the service.
        """
        ...
    def get_combined_size(self) -> int:
        """Get total size of all files in bytes.
        Returns:
            Total size in bytes of all managed files.
        """
        ...
    def add_files(self, paths: List[str], 
                  success_callback: Callable[[List[str]], None],
                  error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Add files to the managed list.
        Args:
            paths: List of file paths to add
            success_callback: Called with list of successfully added files
            error_callback: Called with error message if operation fails
        """
        ...
    def remove_file(self, index: int,
                    success_callback: Callable[[], None],
                    error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Remove file by index.
        Args:
            index: Index of file to remove
            success_callback: Called when file is successfully removed
            error_callback: Called with error message if operation fails
        """
        ...
    def reorder_files(self, new_order: List[str],
                      success_callback: Callable[[], None],
                      error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Reorder files according to new path order.
        Args:
            new_order: List of file paths in desired order
            success_callback: Called when reordering is complete
            error_callback: Called with error message if operation fails
        """
        ...
class IMetadataHandler(Protocol):
    """Protocol for metadata operations."""
    def get_current_metadata(self) -> Dict[str, Any]:
        """Get current metadata state.
        Returns:
            Dictionary containing current metadata.
        """
        ...
    def load_from_file(self, path: str,
                       success_callback: Callable[[Dict[str, Any]], None],
                       error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Load metadata from audio file.
        Args:
            path: Path to audio file
            success_callback: Called with extracted metadata
            error_callback: Called with error message if operation fails
        """
        ...
    def update_field(self, field: str, value: Any,
                     success_callback: Callable[[Dict[str, Any]], None],
                     error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Update a metadata field.
        Args:
            field: Metadata field name
            value: New value for the field
            success_callback: Called with updated metadata
            error_callback: Called with error message if operation fails
        """
        ...
    def get_for_ffmpeg(self) -> Dict[str, str]:
        """Get metadata formatted for FFmpeg.
        Returns:
            Dictionary with FFmpeg-compatible metadata mappings.
        """
        ...
    def clear_metadata(self,
                       success_callback: Callable[[], None],
                       error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Clear all metadata.
        Args:
            success_callback: Called when metadata is cleared
            error_callback: Called with error message if operation fails
        """
        ...
    def set_cover_art(self, path: str,
                      success_callback: Callable[[Optional[str]], None],
                      error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Set cover art from file path.
        Args:
            path: Path to cover art image file
            success_callback: Called with cover art path (None if cleared)
            error_callback: Called with error message if operation fails
        """
        ...
    def get_cover_art_path(self) -> Optional[str]:
        """Get current cover art path.
        Returns:
            Path to current cover art file, or None if no cover art set.
        """
        ...
class IProcessingService(Protocol):
    """Protocol for audio processing operations."""
    def process(self, input_files: List[str], 
                output_path: str,
                output_filename: str,
                metadata: Dict[str, Any],
                settings: Dict[str, Any],
                progress_callback: Callable[[int], None],
                status_callback: Callable[[str], None],
                complete_callback: Callable[[str], None],
                error_callback: Callable[[str], None]) -> None:
        """Process audio files into audiobook format.
        Args:
            input_files: List of input audio file paths
            output_path: Directory for output file
            output_filename: Name of output file
            metadata: Metadata to embed in output
            settings: Processing settings (bitrate, format, etc.)
            progress_callback: Called with progress percentage (0-100)
            status_callback: Called with status messages
            complete_callback: Called with output file path when complete
            error_callback: Called with error message if processing fails
        """
        ...
    def process_preview(self, input_file: str,
                        metadata: Dict[str, Any],
                        settings: Dict[str, Any],
                        duration_seconds: int,
                        complete_callback: Callable[[str], None],
                        error_callback: Callable[[str], None]) -> None:
        """Generate a preview of the processed audio.
        Args:
            input_file: Path to input audio file
            metadata: Metadata to embed
            settings: Processing settings
            duration_seconds: Length of preview in seconds
            complete_callback: Called with preview file path
            error_callback: Called with error message if operation fails
        """
        ...
    def cancel(self,
               success_callback: Callable[[], None],
               error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Cancel current processing operation.
        Args:
            success_callback: Called when cancellation is complete
            error_callback: Called with error message if cancellation fails
        """
        ...
    def is_processing(self) -> bool:
        """Check if processing is currently active.
        Returns:
            True if processing is active, False otherwise.
        """
        ...
class ISettingsManager(Protocol):
    """Protocol for settings management."""
    def get_setting(self, name: str, default: Any = None) -> Any:
        """Get setting value by name.
        Args:
            name: Setting name
            default: Default value if setting not found
        Returns:
            Setting value or default
        """
        ...
    def set_setting(self, name: str, value: Any,
                    success_callback: Callable[[str, Any], None],
                    error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Set setting value.
        Args:
            name: Setting name
            value: New setting value
            success_callback: Called with setting name and value when saved
            error_callback: Called with error message if operation fails
        """
        ...
    def get_all_settings(self) -> Dict[str, Any]:
        """Get all current settings.
        Returns:
            Dictionary containing all settings.
        """
        ...
class IPathService(Protocol):
    """Protocol for path and filename operations."""
    def calculate_output_path(self, base_dir: str, 
                              metadata: Dict[str, Any],
                              use_subdirectory_pattern: bool) -> str:
        """Calculate output directory path.
        Args:
            base_dir: Base output directory
            metadata: Metadata for path generation
            use_subdirectory_pattern: Whether to create subdirectories
        Returns:
            Calculated output directory path
        """
        ...
    def generate_output_filename(self, metadata: Dict[str, Any], 
                                 pattern_id: int) -> str:
        """Generate output filename based on pattern.
        Args:
            metadata: Metadata for filename generation
            pattern_id: Filename pattern identifier (0, 1, or 2)
        Returns:
            Generated filename
        """
        ...
# Error categories for better error handling
class ErrorCategory:
    """Standard error categories for service operations."""
    FILE_NOT_FOUND = "file_not_found"
    PERMISSION_DENIED = "permission_denied"
    INVALID_FORMAT = "invalid_format"
    PROCESSING_ERROR = "processing_error"
    METADATA_ERROR = "metadata_error"
    SETTINGS_ERROR = "settings_error"
    VALIDATION_ERROR = "validation_error"
    DISK_FULL = "disk_full"
    FFMPEG_ERROR = "ffmpeg_error"
    NETWORK_ERROR = "network_error"
class ServiceError(Exception):
    """Base exception for service operations."""
    def __init__(self, message: str, category: str = ErrorCategory.PROCESSING_ERROR):
        super().__init__(message)
        self.message = message
        self.category = category
    def __str__(self) -> str:
        return f"[{self.category}] {self.message}"
# Factory function type for creating services
ServiceFactory = Callable[[], Any]
# Registry for service factories (used by adapters)
SERVICE_REGISTRY: Dict[str, ServiceFactory] = {}
def register_service_factory(interface_name: str, factory: ServiceFactory) -> None:
    """Register a factory function for a service interface.
    Args:
        interface_name: Name of the interface (e.g., "IFileService")
        factory: Factory function that creates service instances
    """
    SERVICE_REGISTRY[interface_name] = factory
def get_service_factory(interface_name: str) -> Optional[ServiceFactory]:
    """Get registered factory for a service interface.
    Args:
        interface_name: Name of the interface
    Returns:
        Factory function or None if not registered
    """
    return SERVICE_REGISTRY.get(interface_name)
```

## File: src/abb/services/service_factory.py
```python
"""Service factory with feature flag support for ABB services.
This module provides a centralized factory for creating service instances
with support for the ABB_PURE_SERVICES feature flag that enables gradual
migration between Qt-based and pure Python service implementations.
"""
import logging
import os
from typing import Optional, Union
from .adapters.file_service_adapter import QtFileServiceAdapter
from .adapters.metadata_handler_adapter import QtMetadataHandlerAdapter
from .adapters.processing_service_adapter import QtProcessingServiceAdapter
from .adapters.settings_manager_adapter import QtSettingsManagerAdapter
# Import core services and adapters
from .core.file_service_core import FileServiceCore
from .core.metadata_handler_core import MetadataHandlerCore
from .core.path_service_core import PathServiceCore
from .core.processing_service_core import ProcessingServiceCore
from .core.settings_manager_core import SettingsManagerCore
# Import current Qt services
from .file_service import FileService
from .interfaces import (
    IPathService,
)
from .metadata_service import MetadataService
from .path_service import PathService
from .processing_service import ProcessingService
from .settings_manager import SettingsManager
logger = logging.getLogger(__name__)
def is_pure_services_enabled() -> bool:
    """Check if pure services are enabled via environment variable.
    Returns:
        True if ABB_PURE_SERVICES is set to 'true' (case insensitive)
    """
    return os.environ.get('ABB_PURE_SERVICES', 'false').lower() == 'true'
def create_file_service() -> Union[FileService, QtFileServiceAdapter]:
    """Create a file service instance based on feature flag.
    Returns:
        FileService instance (Qt-based) or QtFileServiceAdapter wrapping pure service
    """
    if is_pure_services_enabled():
        logger.debug("Creating pure FileService with Qt adapter")
        core_service = FileServiceCore()
        return QtFileServiceAdapter(core_service)
    else:
        logger.debug("Creating Qt-based FileService")
        return FileService()
def create_metadata_service() -> Union[MetadataService, QtMetadataHandlerAdapter]:
    """Create a metadata service instance based on feature flag.
    Returns:
        MetadataService instance (Qt-based) or QtMetadataHandlerAdapter wrapping pure service
    """
    if is_pure_services_enabled():
        logger.debug("Creating pure MetadataHandler with Qt adapter")
        core_service = MetadataHandlerCore()
        return QtMetadataHandlerAdapter(core_service)
    else:
        logger.debug("Creating Qt-based MetadataService")
        return MetadataService()
def create_processing_service(path_service: Optional[IPathService] = None) -> Union[ProcessingService, QtProcessingServiceAdapter]:
    """Create a processing service instance based on feature flag.
    Args:
        path_service: Optional path service dependency. If not provided,
                     will be created based on the same feature flag.
    Returns:
        ProcessingService instance (Qt-based) or QtProcessingServiceAdapter wrapping pure service
    """
    if is_pure_services_enabled():
        logger.debug("Creating pure ProcessingService with Qt adapter")
        # Create path service if not provided
        if path_service is None:
            path_service = create_path_service()
        core_service = ProcessingServiceCore(path_service)
        return QtProcessingServiceAdapter(core_service, path_service)
    else:
        logger.debug("Creating Qt-based ProcessingService")
        return ProcessingService()
def create_settings_manager(settings_file_path: str, 
                           default_settings: Optional[dict] = None) -> Union[SettingsManager, QtSettingsManagerAdapter]:
    """Create a settings manager instance based on feature flag.
    Args:
        settings_file_path: Path to settings file
        default_settings: Optional default settings dictionary
    Returns:
        SettingsManager instance (Qt-based) or QtSettingsManagerAdapter wrapping pure service
    """
    if is_pure_services_enabled():
        logger.debug("Creating pure SettingsManager with Qt adapter")
        core_service = SettingsManagerCore(settings_file_path, default_settings)
        return QtSettingsManagerAdapter(core_service)
    else:
        logger.debug("Creating Qt-based SettingsManager")
        return SettingsManager(settings_file_path, default_settings)
def create_path_service() -> Union[PathService, PathServiceCore]:
    """Create a path service instance based on feature flag.
    Note: PathService is already pure Python, but this factory maintains
    consistency and allows for future Qt-specific enhancements if needed.
    Returns:
        PathService instance (existing) or PathServiceCore (pure implementation)
    """
    if is_pure_services_enabled():
        logger.debug("Creating pure PathServiceCore")
        return PathServiceCore()
    else:
        logger.debug("Creating existing PathService")
        return PathService()
class ServiceFactory:
    """Factory class for creating service instances with caching support.
    This factory provides both factory functions and caching capabilities
    for service instances that should be singletons.
    """
    def __init__(self):
        """Initialize the service factory."""
        self._services = {}
    def get_file_service(self) -> Union[FileService, QtFileServiceAdapter]:
        """Get cached file service or create new one.
        Returns:
            Cached or new file service instance
        """
        if 'file_service' not in self._services:
            self._services['file_service'] = create_file_service()
        return self._services['file_service']
    def get_metadata_service(self) -> Union[MetadataService, QtMetadataHandlerAdapter]:
        """Get cached metadata service or create new one.
        Returns:
            Cached or new metadata service instance
        """
        if 'metadata_service' not in self._services:
            self._services['metadata_service'] = create_metadata_service()
        return self._services['metadata_service']
    def get_processing_service(self, path_service: Optional[IPathService] = None) -> Union[ProcessingService, QtProcessingServiceAdapter]:
        """Get cached processing service or create new one.
        Args:
            path_service: Optional path service dependency
        Returns:
            Cached or new processing service instance
        """
        if 'processing_service' not in self._services:
            self._services['processing_service'] = create_processing_service(path_service)
        return self._services['processing_service']
    def get_settings_manager(self, settings_file_path: str, 
                           default_settings: Optional[dict] = None) -> Union[SettingsManager, QtSettingsManagerAdapter]:
        """Get cached settings manager or create new one.
        Args:
            settings_file_path: Path to settings file
            default_settings: Optional default settings
        Returns:
            Cached or new settings manager instance
        """
        cache_key = f'settings_manager_{settings_file_path}'
        if cache_key not in self._services:
            self._services[cache_key] = create_settings_manager(settings_file_path, default_settings)
        return self._services[cache_key]
    def get_path_service(self) -> Union[PathService, PathServiceCore]:
        """Get cached path service or create new one.
        Returns:
            Cached or new path service instance
        """
        if 'path_service' not in self._services:
            self._services['path_service'] = create_path_service()
        return self._services['path_service']
    def clear_cache(self) -> None:
        """Clear all cached service instances."""
        self._services.clear()
        logger.debug("Service factory cache cleared")
    def get_service_info(self) -> dict:
        """Get information about current service configuration.
        Returns:
            Dictionary with service configuration information
        """
        return {
            'pure_services_enabled': is_pure_services_enabled(),
            'cached_services': list(self._services.keys()),
            'environment_variable': os.environ.get('ABB_PURE_SERVICES', 'not set')
        }
# Global factory instance for convenience
default_factory = ServiceFactory()
# Convenience functions using the default factory
def get_file_service() -> Union[FileService, QtFileServiceAdapter]:
    """Get file service from default factory."""
    return default_factory.get_file_service()
def get_metadata_service() -> Union[MetadataService, QtMetadataHandlerAdapter]:
    """Get metadata service from default factory."""
    return default_factory.get_metadata_service()
def get_processing_service(path_service: Optional[IPathService] = None) -> Union[ProcessingService, QtProcessingServiceAdapter]:
    """Get processing service from default factory."""
    return default_factory.get_processing_service(path_service)
def get_settings_manager(settings_file_path: str, 
                        default_settings: Optional[dict] = None) -> Union[SettingsManager, QtSettingsManagerAdapter]:
    """Get settings manager from default factory."""
    return default_factory.get_settings_manager(settings_file_path, default_settings)
def get_path_service() -> Union[PathService, PathServiceCore]:
    """Get path service from default factory."""
    return default_factory.get_path_service()
def clear_service_cache() -> None:
    """Clear default factory cache."""
    default_factory.clear_cache()
def get_service_info() -> dict:
    """Get service configuration information."""
    return default_factory.get_service_info()
```

## File: src/abb/ui/widgets/dropzone.py
```python
"""DropZone widget for Audiobook Boss.
Provides a drag-and-drop area for audio files with click-to-select functionality.
"""
from pathlib import Path
from typing import List, Set
from PySide6.QtCore import QMimeData, QSettings, Qt, Signal
from PySide6.QtGui import QDragEnterEvent, QDropEvent, QMouseEvent
from PySide6.QtWidgets import QFileDialog, QFrame, QLabel
class DropZone(QLabel):
    """Custom drag-and-drop area for audio files.
    Accepts .mp3, .m4a, .m4b, and .aac files.
    """
    # Signal emitted when files are dropped or selected
    filesDropped = Signal(list)
    # Supported file extensions
    SUPPORTED_EXTENSIONS = {".mp3", ".m4a", ".m4b", ".aac"}
    def __init__(self, text: str = "Drag & Drop files here or Click to Select") -> None:
        """Initialize the DropZone widget."""
        super().__init__(text)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setFrameStyle(QFrame.Shape.StyledPanel | QFrame.Shadow.Raised)
        self.setStyleSheet(
            "border: 2px dashed #d1d5db; border-radius: 6px; color: #6b7280;"
            "font-size: 13px; padding: 24px; background-color: #fafafa;"
        )
        self.setAcceptDrops(True)
        self.setObjectName("dropZoneLabel")
        # Add a secondary line for supported formats
        self.setText(f"{text}\n<small>Supports: mp3, m4a, m4b, aac</small>")
        # Settings for remembering last directory
        self.settings = QSettings("AudiobookBoss", "ABB")
    def dragEnterEvent(self, event: QDragEnterEvent) -> None:
        """Handle drag enter events, accepting only if URLs are present."""
        if event.mimeData().hasUrls():
            # Check if at least one file has a supported extension
            if self._has_supported_files(event.mimeData()):
                self.setStyleSheet(
                    "border: 2px dashed #d1d5db; border-radius: 6px; color: #6b7280;"
                    "font-size: 13px; padding: 24px; background-color: #f0f0f0;"
                )
                event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()
    def dragLeaveEvent(self, event) -> None:
        """Reset styling when drag leaves the widget."""
        self.setStyleSheet(
            "border: 2px dashed #d1d5db; border-radius: 6px; color: #6b7280;"
            "font-size: 13px; padding: 24px; background-color: #fafafa;"
        )
        super().dragLeaveEvent(event)
    def dropEvent(self, event: QDropEvent) -> None:
        """Handle drop events, processing valid audio files."""
        if event.mimeData().hasUrls():
            file_paths = self._process_urls(event.mimeData())
            if file_paths:
                self.filesDropped.emit(file_paths)
            # Reset styling
            self.setStyleSheet(
                "border: 2px dashed #d1d5db; border-radius: 6px; color: #6b7280;"
                "font-size: 13px; padding: 24px; background-color: #fafafa;"
            )
            event.acceptProposedAction()
        else:
            event.ignore()
    def mouseReleaseEvent(self, event: QMouseEvent) -> None:
        """Handle mouse click to open file dialog."""
        if event.button() == Qt.MouseButton.LeftButton:
            # Get the last used directory or default to home
            last_dir = self.settings.value("last_input_dir", str(Path.home()), type=str)
            # Open file dialog
            paths, _ = QFileDialog.getOpenFileNames(
                self,
                "Select Audio Files",
                last_dir,
                "Audio Files (*.mp3 *.m4a *.m4b *.aac)",
            )
            if paths:
                # Save the directory for next time
                new_dir = str(Path(paths[0]).parent)
                self.settings.setValue("last_input_dir", new_dir)
                # Process and emit the file paths
                file_paths = self._normalize_paths(paths)
                self.filesDropped.emit(file_paths)
    def _has_supported_files(self, mime_data: QMimeData) -> bool:
        """Check if the mime data contains at least one supported file."""
        for url in mime_data.urls():
            file_path = url.toLocalFile()
            if file_path:
                suffix = Path(file_path).suffix.lower()
                if suffix in self.SUPPORTED_EXTENSIONS:
                    return True
        return False
    def _process_urls(self, mime_data: QMimeData) -> List[str]:
        """Process URLs from mime data, filtering for supported audio files."""
        file_paths = []
        for url in mime_data.urls():
            file_path = url.toLocalFile()
            if file_path:
                path = Path(file_path)
                if path.suffix.lower() in self.SUPPORTED_EXTENSIONS:
                    file_paths.append(str(path))
        return self._normalize_paths(file_paths)
    def _normalize_paths(self, paths: List[str]) -> List[str]:
        """Normalize paths and remove duplicates while preserving order."""
        # Initialize empty list for ordered unique paths and a set for tracking seen paths
        ordered_unique_paths: List[str] = []
        seen_paths: Set[str] = set()
        # Iterate through paths in original order
        for path in paths:
            # Resolve to absolute path
            abs_path_str = str(Path(path).resolve())
            # Add to ordered list only if not seen before
            if abs_path_str not in seen_paths:
                ordered_unique_paths.append(abs_path_str)
                seen_paths.add(abs_path_str)
        return ordered_unique_paths
```

## File: src/abb/processing_validator.py
```python
"""Validates requirements before starting audio processing."""
import os
import shutil
from pathlib import Path
from typing import List, Optional, Tuple
class ProcessingValidator:
    """Validates all prerequisites for audio processing."""
    def __init__(self):
        """Initialize the validator."""
        pass
    def validate_all(
        self, 
        input_files: List[Path], 
        output_directory: Path,
        estimated_size_mb: float
    ) -> Tuple[bool, Optional[str]]:
        """Validate all processing requirements.
        Args:
            input_files: List of input file paths
            output_directory: Target output directory
            estimated_size_mb: Estimated output size in MB
        Returns:
            Tuple of (success, error_message)
        """
        # Check input files
        success, error = self._validate_input_files(input_files)
        if not success:
            return False, error
        # Check output directory
        success, error = self._validate_output_directory(output_directory)
        if not success:
            return False, error
        # Check disk space
        success, error = self._validate_disk_space(output_directory, estimated_size_mb)
        if not success:
            return False, error
        return True, None
    def _validate_input_files(self, input_files: List[Path]) -> Tuple[bool, Optional[str]]:
        """Validate that all input files exist and are readable."""
        if not input_files:
            return False, "No input files provided"
        for file_path in input_files:
            if not file_path.exists():
                return False, f"Input file not found: {file_path}"
            if not file_path.is_file():
                return False, f"Not a file: {file_path}"
            if not os.access(file_path, os.R_OK):
                return False, f"File not readable: {file_path}"
        return True, None
    def _validate_output_directory(self, output_directory: Path) -> Tuple[bool, Optional[str]]:
        """Validate that output directory exists and is writable."""
        try:
            # Create directory if it doesn't exist
            output_directory.mkdir(parents=True, exist_ok=True)
            # Check if writable
            test_file = output_directory / ".write_test"
            try:
                test_file.touch()
                test_file.unlink()
            except Exception:
                return False, f"Output directory not writable: {output_directory}"
            return True, None
        except Exception as e:
            return False, f"Cannot create output directory: {e}"
    def _validate_disk_space(
        self, 
        output_directory: Path, 
        estimated_size_mb: float
    ) -> Tuple[bool, Optional[str]]:
        """Validate sufficient disk space (estimated_size * 1.2)."""
        try:
            # Get disk usage stats
            stat = shutil.disk_usage(output_directory)
            available_mb = stat.free / (1024 * 1024)
            # Require 20% buffer
            required_mb = estimated_size_mb * 1.2
            if available_mb < required_mb:
                return False, (
                    f"Insufficient disk space. "
                    f"Required: {required_mb:.1f} MB, "
                    f"Available: {available_mb:.1f} MB"
                )
            return True, None
        except Exception:
            # If we can't check disk space, warn but don't fail
            return True, None
```

## File: src/abb/services/path_service.py
```python
"""Path service for output path calculation and filename generation."""
from pathlib import Path
from typing import Any, Dict
class PathService:
    """Service for calculating output paths and generating filenames for audiobook processing."""
    def calculate_output_path(self, base_dir: str, metadata: Dict[str, Any], 
                            use_subdirectory_pattern: bool) -> str:
        """Calculate the output directory path based on settings and metadata.
        Args:
            base_dir: Base output directory path.
            metadata: Dictionary containing audio metadata (artist, series).
            use_subdirectory_pattern: Whether to create subdirectories for author/series.
        Returns:
            Full output directory path as string.
        """
        output_path = Path(base_dir)
        if use_subdirectory_pattern:
            author = self._safe_str(metadata.get("artist", "")).strip()
            if author:
                output_path = output_path / self._sanitize_path(author)
            series = self._safe_str(metadata.get("series", "")).strip()
            if series:
                output_path = output_path / self._sanitize_path(series)
        return str(output_path)
    def generate_output_filename(self, metadata: Dict[str, Any], pattern_id: int) -> str:
        """Generate output filename based on pattern and metadata.
        Args:
            metadata: Dictionary containing audio metadata (title, artist, series, year).
            pattern_id: Pattern to use (0=Title (Year), 1=Author - Title, 2=Series - Title).
        Returns:
            Sanitized filename string with .m4b extension.
        """
        filename = "audiobook.m4b"  # Default
        if pattern_id == 0:  # Title (Year)
            title = self._safe_str(metadata.get("title", ""))
            year = self._safe_str(metadata.get("year", ""))
            if title.strip():
                filename = f"{title} ({year}).m4b" if year.strip() else f"{title}.m4b"
        elif pattern_id == 1:  # Author - Title
            author = self._safe_str(metadata.get("artist", ""))
            title = self._safe_str(metadata.get("title", ""))
            if author.strip() and title.strip():
                filename = f"{author} - {title}.m4b"
            elif title.strip():
                filename = f"{title}.m4b"
        elif pattern_id == 2:  # Series - Title
            series = self._safe_str(metadata.get("series", ""))
            title = self._safe_str(metadata.get("title", ""))
            if series.strip() and title.strip():
                filename = f"{series} - {title}.m4b"
            elif title.strip():
                filename = f"{title}.m4b"
        # Basic check for empty name
        if not filename.strip() or filename.strip() in [".m4b", "().m4b", "( ).m4b"]:
            filename = "audiobook.m4b"
        return self._sanitize_filename(filename)
    def _sanitize_path(self, path_str: str) -> str:
        """Remove invalid characters from path components.
        Args:
            path_str: Path string to sanitize.
        Returns:
            Sanitized path string with invalid characters replaced by underscores.
        """
        invalid_chars = ["<", ">", ":", '"', "/", "\\", "|", "?", "*"]
        for char in invalid_chars:
            path_str = path_str.replace(char, "_")
        return path_str
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for filesystem compatibility.
        Args:
            filename: Filename to sanitize.
        Returns:
            Sanitized filename with invalid characters replaced.
        """
        return self._sanitize_path(filename)
    def _safe_str(self, val: Any) -> str:
        """Safely convert value to string.
        Args:
            val: Value to convert (can be None).
        Returns:
            String representation of value or empty string if None.
        """
        return str(val) if val is not None else ""
```

## File: src/abb/services/unified_metadata_handler.py
```python
"""Unified Metadata Handler for Audiobook Boss.
A facade pattern that wraps existing metadata extraction functions while providing
a unified interface for metadata management. Enables gradual migration through
the ABB_NEW_META environment variable.
This handler maintains 100% compatibility with existing functions while providing
a cleaner interface for future development.
"""
import os
from typing import Any, Dict, Optional
# Import existing functions to wrap
from ..metadata_utils import (
    apply_metadata_defaults,
    clear_metadata_cache,
    extract_cover,
    extract_metadata,
    extract_tags,
)
class UnifiedMetadataHandler:
    """Unified facade for metadata operations.
    Wraps existing metadata functions while providing a cleaner interface
    and state management for metadata operations.
    """
    # Single source of truth for ABB to FFmpeg metadata mapping
    # This mapping is used throughout the application for consistency
    ABB_TO_FFMPEG_METADATA_MAP_GENERAL = {
        "title": "title",
        "album": "album",
        "year": "date",
        "genre": "genre",
        "narrator": "composer",
        "series": "mood",
        "series_pos": "track",
        "copyright": "copyright",
        "sort_title": "title_sort",
        "sort_artist": "artist_sort",
        "sort_album_artist": "album_artist_sort",
        "sort_composer": "composer_sort",
        "disc_number": "disc",
        "compilation": "compilation",
        "grouping": "grouping",
        "lyrics": "lyrics",
        "publisher": "publisher",
        "comment": "comment",
        "description": "description"
    }
    def __init__(self):
        """Initialize the handler with empty state."""
        self._current_metadata: Dict[str, Any] = {}
        self._current_file_path: Optional[str] = None
    # Primary compatibility interfaces - wrap existing functions exactly
    def load_from_file(self, file_path: str) -> Dict[str, Any]:
        """Load metadata from a file (wraps extract_metadata).
        Args:
            file_path: Path to the audio file
        Returns:
            Dictionary containing metadata fields with defaults applied
        """
        metadata = extract_metadata(file_path)
        self._current_metadata = metadata.copy()
        self._current_file_path = file_path
        return metadata
    def extract_tags_only(self, file_path: str) -> dict:
        """Extract basic tags only (wraps extract_tags).
        Args:
            file_path: Path to the audio file
        Returns:
            Dictionary with basic tags, None for missing values
        """
        return extract_tags(file_path)
    def extract_cover_art(self, file_path: str) -> Optional[bytes]:
        """Extract cover art as bytes (wraps extract_cover).
        Args:
            file_path: Path to the audio file
        Returns:
            Cover art as bytes or None if not found
        """
        return extract_cover(file_path)
    # New unified interface for state management
    def update_field(self, field: str, value: Any) -> None:
        """Update a metadata field in the current state.
        Args:
            field: The metadata field name (e.g., 'title', 'artist')
            value: The new value for the field
        """
        self._current_metadata[field] = value
    def get_current_metadata(self) -> Dict[str, Any]:
        """Get the current metadata state.
        Returns:
            Copy of current metadata dictionary
        """
        return self._current_metadata.copy()
    def get_for_ffmpeg(self) -> Dict[str, str]:
        """Get metadata formatted for FFmpeg command building.
        Uses the ABB_TO_FFMPEG_METADATA_MAP_GENERAL mapping
        to transform metadata fields into FFmpeg-compatible format.
        Returns:
            Dictionary with FFmpeg metadata keys and string values
        """
        ffmpeg_metadata = {}
        # Use the unified mapping - single source of truth
        for abb_key, ffmpeg_key in self.ABB_TO_FFMPEG_METADATA_MAP_GENERAL.items():
            value = self._current_metadata.get(abb_key)
            if value is not None and str(value).strip() != "":
                ffmpeg_metadata[ffmpeg_key] = str(value)
        # Handle special mappings (from legacy command_builder.py logic)
        artist_val = self._current_metadata.get('artist')
        if artist_val:
            ffmpeg_metadata['artist'] = str(artist_val)
            # Set album_artist to artist if not explicitly set
            if 'album_artist' not in ffmpeg_metadata:
                ffmpeg_metadata['album_artist'] = str(artist_val)
        # Handle comment/description priority
        comment_val = self._current_metadata.get('comment')
        description_val = self._current_metadata.get('description')
        if comment_val:
            ffmpeg_metadata['comment'] = str(comment_val)
        elif description_val:
            ffmpeg_metadata['comment'] = str(description_val)
        # Handle series_sort priority for album_sort
        series_sort_val = self._current_metadata.get('series_sort')
        sort_album_val = self._current_metadata.get('sort_album')
        if series_sort_val:
            ffmpeg_metadata['album_sort'] = str(series_sort_val)
        elif sort_album_val:
            ffmpeg_metadata['album_sort'] = str(sort_album_val)
        return ffmpeg_metadata
    def apply_defaults(self) -> None:
        """Apply default metadata logic to current state."""
        self._current_metadata = apply_metadata_defaults(self._current_metadata)
    def clear_cache(self) -> None:
        """Clear the metadata cache."""
        clear_metadata_cache()
    def reset_state(self) -> None:
        """Reset the handler's internal state."""
        self._current_metadata = {}
        self._current_file_path = None
    # Utility methods
    def get_current_file_path(self) -> Optional[str]:
        """Get the file path of currently loaded metadata.
        Returns:
            Current file path or None if no file loaded
        """
        return self._current_file_path
    def has_metadata(self) -> bool:
        """Check if metadata is currently loaded.
        Returns:
            True if metadata is loaded, False otherwise
        """
        return bool(self._current_metadata)
    def get_field(self, field: str, default: Any = None) -> Any:
        """Get a specific metadata field value.
        Args:
            field: The field name to retrieve
            default: Default value if field not found
        Returns:
            The field value or default
        """
        return self._current_metadata.get(field, default)
    @staticmethod
    def is_enabled() -> bool:
        """Check if the unified metadata handler should be used.
        Reads the ABB_NEW_META environment variable.
        Returns:
            True if ABB_NEW_META is set to 'true' (case-insensitive), False otherwise
        """
        return os.environ.get('ABB_NEW_META', 'False').lower() == 'true'
    @classmethod
    def get_metadata_mapping(cls) -> Dict[str, str]:
        """Get the ABB to FFmpeg metadata mapping.
        Provides access to the single source of truth mapping without
        requiring an instance of the handler.
        Returns:
            Dictionary mapping ABB field names to FFmpeg field names
        """
        return cls.ABB_TO_FFMPEG_METADATA_MAP_GENERAL.copy()
# Convenience factory function for backward compatibility
def create_metadata_handler() -> UnifiedMetadataHandler:
    """Create a new UnifiedMetadataHandler instance.
    Returns:
        New handler instance
    """
    return UnifiedMetadataHandler()
```

## File: src/abb/ui/dialogs/about_dialog.py
```python
import logging
from PySide6.QtCore import Qt
from PySide6.QtWidgets import QDialog, QHBoxLayout, QLabel, QPushButton, QVBoxLayout
logger = logging.getLogger("AudiobookBoss.AboutDialog")
class AboutDialog(QDialog):
    """Dialog displaying information about the application."""
    def __init__(self, parent=None):
        """Initialize the about dialog.
        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        self.setWindowTitle("About Audiobook Boss")
        self.setMinimumWidth(400)
        self.setMinimumHeight(300)
        self._setup_ui()
        logger.debug("About dialog initialized")
    def _setup_ui(self):
        """Set up the dialog UI with information and close button."""
        main_layout = QVBoxLayout(self)
        main_layout.setAlignment(Qt.AlignCenter)
        title_label = QLabel("Audiobook Boss")
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold;")
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        self.version_label = QLabel("Version 1.0.0")
        self.version_label.setObjectName("version_label")
        self.version_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(self.version_label)
        main_layout.addSpacing(20)
        self.license_label = QLabel("License: MIT")
        self.license_label.setObjectName("license_label")
        self.license_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(self.license_label)
        credits_text = (
            "Credits:\n"
            "Developed by the Audiobook Boss Team\n"
            "Using PySide6 (Qt for Python)\n"
            "FFmpeg for audio processing"
        )
        self.credits_label = QLabel(credits_text)
        self.credits_label.setObjectName("credits_label")
        self.credits_label.setAlignment(Qt.AlignCenter)
        self.credits_label.setWordWrap(True)
        main_layout.addWidget(self.credits_label)
        main_layout.addSpacing(20)
        button_layout = QHBoxLayout()
        button_layout.setAlignment(Qt.AlignCenter)
        self.close_button = QPushButton("Close")
        self.close_button.setObjectName("close_button")
        self.close_button.clicked.connect(self.close)
        button_layout.addWidget(self.close_button)
        main_layout.addLayout(button_layout)
```

## File: src/abb/main.py
```python
"""Main entry point for Audiobook Boss (ABB).
Bootstraps QApplication and launches MainWindow.
"""
import os
import sys
from PySide6.QtWidgets import QApplication
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
if PROJECT_ROOT not in sys.path:
    sys.path.insert(0, PROJECT_ROOT)
from src.abb.ffmpeg_utils import setup_ffmpeg_path  # noqa: E402
from src.abb.main_window import MainWindow  # noqa: E402
def main() -> None:
    """Application entry point."""
    setup_ffmpeg_path()
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
if __name__ == "__main__":
    # The following block for venv check is proposed for removal due to potential issues.
    # venv_python = os.path.join(PROJECT_ROOT, ".venv", "bin", "python")
    # if sys.executable != os.path.realpath(venv_python) and os.path.exists(venv_python):
    #     pass
    main()
```

## File: src/abb/ui/dialogs/settings_dialog.py
```python
import logging
from typing import Any, Dict
from PySide6.QtWidgets import (
    QCheckBox,
    QDialog,
    QDialogButtonBox,
    QFileDialog,
    QFormLayout,
    QHBoxLayout,
    QLineEdit,
    QPushButton,
    QTabWidget,
    QVBoxLayout,
    QWidget,
)
logger = logging.getLogger("AudiobookBoss.SettingsDialog")
class SettingsDialog(QDialog):
    """Dialog for configuring application settings."""
    def __init__(self, settings: Dict[str, Any], parent=None):
        """Initialize the settings dialog with the current settings.
        Args:
            settings: Dictionary containing the current application settings
            parent: Parent widget
        """
        super().__init__(parent)
        self.setWindowTitle("Settings")
        self.setMinimumWidth(500)
        self.initial_settings = settings.copy() # For comparison on accept/reject if needed
        self.updated_settings = settings.copy() # To store changes made in UI
        self._setup_ui()
        self._populate_settings()
        logger.debug("Settings dialog initialized")
    def _setup_ui(self):
        """Set up the dialog UI with tabs and controls."""
        main_layout = QVBoxLayout(self)
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        self._setup_theme_tab()
        self._setup_paths_tab()
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)
    def _setup_theme_tab(self):
        """Set up the Theme tab with appearance settings."""
        theme_tab = QWidget()
        layout = QVBoxLayout(theme_tab)
        self.dark_mode_checkbox = QCheckBox("Use Dark Mode")
        self.dark_mode_checkbox.setObjectName("dark_mode_checkbox")
        self.dark_mode_checkbox.stateChanged.connect(self._on_dark_mode_changed)
        layout.addWidget(self.dark_mode_checkbox)
        layout.addStretch() # Push controls to the top
        self.tab_widget.addTab(theme_tab, "Theme")
    def _setup_paths_tab(self):
        """Set up the Paths tab with file path settings."""
        paths_tab = QWidget()
        layout = QFormLayout(paths_tab)
        ffmpeg_layout = QHBoxLayout()
        self.ffmpeg_path_edit = QLineEdit()
        self.ffmpeg_path_edit.setObjectName("ffmpeg_path_edit")
        self.ffmpeg_path_edit.textChanged.connect(self._on_ffmpeg_path_changed)
        ffmpeg_layout.addWidget(self.ffmpeg_path_edit)
        self.ffmpeg_browse_button = QPushButton("Browse...")
        self.ffmpeg_browse_button.setObjectName("ffmpeg_browse_button")
        self.ffmpeg_browse_button.clicked.connect(self._browse_ffmpeg_path)
        ffmpeg_layout.addWidget(self.ffmpeg_browse_button)
        layout.addRow("FFmpeg Path:", ffmpeg_layout)
        ffprobe_layout = QHBoxLayout()
        self.ffprobe_path_edit = QLineEdit()
        self.ffprobe_path_edit.setObjectName("ffprobe_path_edit")
        self.ffprobe_path_edit.textChanged.connect(self._on_ffprobe_path_changed)
        ffprobe_layout.addWidget(self.ffprobe_path_edit)
        self.ffprobe_browse_button = QPushButton("Browse...")
        self.ffprobe_browse_button.setObjectName("ffprobe_browse_button")
        self.ffprobe_browse_button.clicked.connect(self._browse_ffprobe_path)
        ffprobe_layout.addWidget(self.ffprobe_browse_button)
        layout.addRow("FFprobe Path:", ffprobe_layout)
        self.tab_widget.addTab(paths_tab, "Paths")
    def _populate_settings(self):
        """Populate the UI with the current settings."""
        # Example for "dark_mode" which might map to a "theme" setting
        # This assumes "theme":"dark" means dark mode is checked.
        self.dark_mode_checkbox.setChecked(self.initial_settings.get("dark_mode", False))
        self.ffmpeg_path_edit.setText(self.initial_settings.get("ffmpeg_path", ""))
        # Assuming "ffprobe_path" is a key that will be in initial_settings if managed
        self.ffprobe_path_edit.setText(self.initial_settings.get("ffprobe_path", ""))
    def _on_dark_mode_changed(self, state):
        """Handle dark mode checkbox state changes."""
        # Update the "theme" setting based on dark mode state
        self.updated_settings["dark_mode"] = bool(state)
        self.updated_settings["theme"] = "dark" if bool(state) else "light"
        logger.debug(f"Theme setting changed to: {self.updated_settings['theme']} (dark_mode: {self.updated_settings['dark_mode']})")
    def _on_ffmpeg_path_changed(self, path):
        """Handle FFmpeg path changes."""
        self.updated_settings["ffmpeg_path"] = path
        logger.debug(f"FFmpeg path changed to: {path}")
    def _on_ffprobe_path_changed(self, path):
        """Handle FFprobe path changes."""
        self.updated_settings["ffprobe_path"] = path
        logger.debug(f"FFprobe path changed to: {path}")
    def _browse_ffmpeg_path(self):
        """Open file dialog to browse for FFmpeg executable."""
        path, _ = QFileDialog.getOpenFileName(
            self,
            "Select FFmpeg Executable",
            self.ffmpeg_path_edit.text(),
            "All Files (*)"
        )
        if path:
            self.ffmpeg_path_edit.setText(path)
    def _browse_ffprobe_path(self):
        """Open file dialog to browse for FFprobe executable."""
        path, _ = QFileDialog.getOpenFileName(
            self,
            "Select FFprobe Executable",
            self.ffprobe_path_edit.text(),
            "All Files (*)"
        )
        if path:
            self.ffprobe_path_edit.setText(path)
    def get_updated_settings(self) -> Dict[str, Any]:
        """Get the updated settings dictionary.
        Returns:
            Dictionary containing the updated settings
        """
        return self.updated_settings
```

## File: src/abb/ffmpeg/command_builder.py
```python
import os
from typing import Any, Dict, List, Optional
class FFmpegCommandBuilder:
    """Builds FFmpeg commands for audio processing"""
    # DEPRECATED: Use UnifiedMetadataHandler.get_metadata_mapping() instead
    # This constant is kept only for backward compatibility with tests
    # All runtime logic now uses UnifiedMetadataHandler as single source of truth
    ABB_TO_FFMPEG_METADATA_MAP_GENERAL = {
        "title": "title",
        "album": "album",
        "year": "date",
        "genre": "genre",
        "narrator": "composer",
        "series": "mood",
        "series_pos": "track",
        "copyright": "copyright",
        "sort_title": "title_sort",
        "sort_artist": "artist_sort",
        "sort_album_artist": "album_artist_sort",
        "sort_composer": "composer_sort",
        "disc_number": "disc",
        "compilation": "compilation",
        "grouping": "grouping",
        "lyrics": "lyrics",
        "publisher": "publisher",
        "comment": "comment",
        "description": "description"
    }
    def __init__(self, codec_check_func):
        """Initialize with a function to check codec availability"""
        self._codec_available = codec_check_func
        self._metadata_handler = None  # Lazy-loaded when ABB_NEW_META=True
    def build_ffmpeg_command(
        self,
        input_files: List[str],
        output_file_full_path: str,
        metadata: Dict[str, Any],
        settings: Dict[str, Any],
        ffmpeg_exe_path: str,
        ffprobe_exe_path: Optional[str] = None,
    ) -> List[str]:
        """Build complete FFmpeg command for processing"""
        cmd = []
        cmd.append(ffmpeg_exe_path)
        cmd.extend(["-hide_banner", "-loglevel", "error", "-y"])
        # Input files
        for input_file in input_files:
            cmd.extend(["-i", input_file])
        # Cover art
        cover_art_path = metadata.get("cover_art_temp_path")
        cover_art_stream_idx: Optional[int] = None
        if cover_art_path:
            cmd.extend(["-i", cover_art_path])
            cover_art_stream_idx = len(input_files)
        # Handle multiple inputs
        if len(input_files) > 1:
            filter_parts = []
            for i in range(len(input_files)):
                filter_parts.append(f"[{i}:a]")
            filter_parts.append(f"concat=n={len(input_files)}:v=0:a=1[outa]")
            cmd.extend(["-filter_complex", "".join(filter_parts)])
            cmd.extend(["-map", "[outa]"])
        elif input_files:
            cmd.extend(["-map", "0:a:0"])
        # Map cover art
        if cover_art_stream_idx is not None:
            cmd.extend(["-map", f"{cover_art_stream_idx}:v"])
            cmd.extend(["-c:v", "copy"])
            cmd.extend(["-disposition:v", "attached_pic"])
        # Apply settings
        self._apply_audio_settings_to_command(cmd, settings)
        self._apply_metadata_to_command(cmd, metadata)
        # Handle chapters for single M4A/M4B input
        if len(input_files) == 1:
            first_input_lower = input_files[0].lower()
            if first_input_lower.endswith(".m4a") or first_input_lower.endswith(".m4b"):
                cmd.extend(["-map_chapters", "0"])
        # Output format
        cmd.extend(["-f", "mp4"])
        cmd.extend(["-movflags", "+faststart"])
        cmd.append(output_file_full_path)
        return cmd
    def build_ffmpeg_preview_command(
        self,
        input_file_path: str,
        metadata: Dict[str, Any],
        settings: Dict[str, Any],
        temp_cover_path: Optional[str] = None,
        duration_seconds: int = 30,
    ) -> List[str]:
        """Build FFmpeg command for preview generation"""
        import tempfile
        from ..ffmpeg_utils import _get_executable_path
        ffmpeg_exe_path = _get_executable_path("ffmpeg")
        if not ffmpeg_exe_path:
            raise FileNotFoundError("FFmpeg executable not found")
        cmd = []
        cmd.append(str(ffmpeg_exe_path))
        cmd.extend(["-hide_banner", "-loglevel", "error", "-y"])
        cmd.extend(["-i", input_file_path])
        cover_art_stream_idx: Optional[int] = None
        if temp_cover_path:
            cmd.extend(["-i", temp_cover_path])
            cover_art_stream_idx = 1
        cmd.extend(["-map", "0:a:0"])
        if cover_art_stream_idx is not None:
            cmd.extend(["-map", f"{cover_art_stream_idx}:v"])
            cmd.extend(["-c:v", "copy"])
            cmd.extend(["-disposition:v", "attached_pic"])
        self._apply_audio_settings_to_command(cmd, settings)
        self._apply_metadata_to_command(cmd, metadata)
        cmd.extend(["-t", str(duration_seconds)])
        cmd.extend(["-f", "mp4"])
        cmd.extend(["-movflags", "+faststart"])
        output_file = tempfile.mktemp(suffix=".m4b")
        cmd.append(output_file)
        return cmd
    def _apply_metadata_to_command(self, cmd: List[str], metadata: Dict[str, Any]) -> None:
        """Apply metadata tags to FFmpeg command"""
        # Check if we should use UnifiedMetadataHandler for mapping
        if self._should_use_unified_handler():
            ffmpeg_tags_to_apply = self._get_ffmpeg_tags_via_handler(metadata)
        else:
            ffmpeg_tags_to_apply = self._get_ffmpeg_tags_legacy(metadata)
        # Apply all tags
        for tag_key, tag_value in ffmpeg_tags_to_apply.items():
            if tag_value is not None and str(tag_value).strip() != "":
                cmd.extend(["-metadata", f"{tag_key}={tag_value}"])
    def _should_use_unified_handler(self) -> bool:
        """Check if UnifiedMetadataHandler should be used for mappings."""
        return os.environ.get('ABB_NEW_META', 'False').lower() == 'true'
    def _get_ffmpeg_tags_via_handler(self, metadata: Dict[str, Any]) -> Dict[str, str]:
        """Get FFmpeg tags using UnifiedMetadataHandler (ABB_NEW_META=True path)."""
        # Lazy-load the handler to avoid circular imports
        if self._metadata_handler is None:
            from ..services.unified_metadata_handler import UnifiedMetadataHandler
            self._metadata_handler = UnifiedMetadataHandler()
        # Set the metadata in the handler and get formatted output
        self._metadata_handler._current_metadata = metadata.copy()
        return self._metadata_handler.get_for_ffmpeg()
    def _get_ffmpeg_tags_legacy(self, metadata: Dict[str, Any]) -> Dict[str, str]:
        """Get FFmpeg tags using legacy logic (ABB_NEW_META=False path).
        Even in legacy mode, we use UnifiedMetadataHandler as the single source
        of truth for the mapping, but apply the logic manually for backward compatibility.
        """
        from ..services.unified_metadata_handler import UnifiedMetadataHandler
        ffmpeg_tags_to_apply: Dict[str, str] = {}
        # Handle artist/album_artist
        artist_val = metadata.get('artist')
        album_artist_val = metadata.get('album_artist')
        if artist_val:
            ffmpeg_tags_to_apply['artist'] = str(artist_val)
        if album_artist_val:
            ffmpeg_tags_to_apply['album_artist'] = str(album_artist_val)
        elif artist_val:
            ffmpeg_tags_to_apply['album_artist'] = str(artist_val)
        # Handle comment/description
        comment_val = metadata.get('comment')
        description_val = metadata.get('description')
        if comment_val:
            ffmpeg_tags_to_apply['comment'] = str(comment_val)
        elif description_val:
            ffmpeg_tags_to_apply['comment'] = str(description_val)
        # Handle series_sort/sort_album
        series_sort_val = metadata.get('series_sort')
        sort_album_val = metadata.get('sort_album')
        if series_sort_val:
            ffmpeg_tags_to_apply['album_sort'] = str(series_sort_val)
        elif sort_album_val:
            ffmpeg_tags_to_apply['album_sort'] = str(sort_album_val)
        # Map general metadata using UnifiedMetadataHandler as single source of truth
        metadata_mapping = UnifiedMetadataHandler.get_metadata_mapping()
        for abb_key, ffmpeg_key in metadata_mapping.items():
            value = metadata.get(abb_key)
            if value is not None and str(value).strip() != "":
                if ffmpeg_key not in ffmpeg_tags_to_apply:
                    ffmpeg_tags_to_apply[ffmpeg_key] = str(value)
        return ffmpeg_tags_to_apply
    def _apply_audio_settings_to_command(self, cmd: List[str], settings: Dict[str, Any]) -> None:
        """Apply audio codec and settings to FFmpeg command"""
        audio_codec = "libfdk_aac" if self._codec_available("libfdk_aac") else "aac"
        cmd.extend(["-c:a", audio_codec])
        if settings.get("bitrate"):
            cmd.extend(["-b:a", f"{settings['bitrate']}k"])
        if settings.get("channels"):
            cmd.extend(["-ac", str(settings['channels'])])
        if settings.get("sample_rate"):
            cmd.extend(["-ar", str(settings['sample_rate'])])
```

## File: src/abb/services/file_service.py
```python
"""FileService for Audiobook Boss.
Handles file operations like adding, removing, and reordering files.
"""
import os
from pathlib import Path
from typing import List
from PySide6.QtCore import QObject, Signal
class FileService(QObject):
    """Service for managing audio files.
    Handles operations like adding, removing, and reordering files.
    Validates file types and normalizes paths.
    Emits signals when the file list changes.
    """
    files_changed = Signal(list) # Signal emitted when the file list changes
    combined_size_changed_signal = Signal(str)  # New signal for combined size
    def __init__(self):
        """Initialize the FileService with an empty file list."""
        super().__init__()
        self._files = []
        self._valid_extensions = {".mp3", ".m4a", ".m4b", ".aac"}
    def get_files(self) -> List[str]:
        """Get the current list of files.
        Returns:
            List of file paths
        """
        return self._files.copy()
    def get_combined_size(self) -> int:
        """Get the combined size of all files in bytes.
        """
        total = 0
        for f in self._files:
            try:
                total += os.path.getsize(f)
            except Exception:
                pass
        return total
    def _emit_combined_size(self):
        size = self.get_combined_size()
        # Format size for display (human readable)
        if size < 1024:
            size_str = f"{size} B"
        elif size < 1024 * 1024:
            size_str = f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            size_str = f"{size / (1024 * 1024):.1f} MB"
        else:
            size_str = f"{size / (1024 * 1024 * 1024):.2f} GB"
        self.combined_size_changed_signal.emit(size_str)
    def add_files(self, paths: List[str]) -> List[str]:
        """Add files to the list, normalizing paths and filtering by extension.
        Args:
            paths: List of file paths to add
        Returns:
            List of file paths that were actually added
        """
        normalized_paths = []
        # Use a set of normalized existing paths for efficient lookup
        existing_paths_lookup = {self._normalize_path(p) for p in self._files}
        for path in paths:
            normalized = self._normalize_path(path)
            if normalized in existing_paths_lookup: # Check against the lookup set
                continue
            if not self._is_valid_audio_file(normalized):
                continue
            normalized_paths.append(normalized)
            existing_paths_lookup.add(normalized) # Add to lookup set as well
        if normalized_paths:
            self._files.extend(normalized_paths)
            self.files_changed.emit(self._files.copy())
            self._emit_combined_size()
        return normalized_paths
    def remove_file(self, index: int) -> None:
        """Remove a file from the list by index.
        Args:
            index: Index of the file to remove
        """
        if 0 <= index < len(self._files):
            self._files.pop(index)
            self.files_changed.emit(self._files.copy())
            self._emit_combined_size()
    def reorder_files(self, new_order_paths: list) -> None:
        """Reorder files by setting a new order based on provided file paths.
        Args:
            new_order_paths: List of file paths representing the new order
        """
        self._files = new_order_paths
        self.files_changed.emit(self._files.copy())
        self._emit_combined_size()
    def _normalize_path(self, path: str) -> str:
        """Normalize a file path to its absolute, canonical form.
        Args:
            path: File path to normalize
        Returns:
            Normalized path
        """
        return str(Path(path).resolve())
    def _is_valid_audio_file(self, path: str) -> bool:
        """Check if a file has a valid audio extension.
        Args:
            path: File path to check
        Returns:
            True if the file has a valid audio extension, False otherwise
        """
        ext = Path(path).suffix.lower()
        return ext in self._valid_extensions
```

## File: src/abb/services/settings_manager.py
```python
"""Settings manager for application configuration persistence."""
import json
import logging
import os
from PySide6.QtCore import QObject, Signal
logger = logging.getLogger(__name__)
class SettingsManager(QObject):
    """Manages application settings persistence and validation."""
    settings_changed = Signal(str, object)
    def __init__(self, settings_file_path, default_settings=None):
        """Initialize the settings manager.
        Args:
            settings_file_path: Path to the JSON settings file.
            default_settings: Optional dictionary of default settings.
        """
        super().__init__()
        self.settings_file_path = settings_file_path
        self._settings = {}
        if default_settings:
            self._settings.update(default_settings)
        self._load_settings()
    def _load_settings(self):
        """Load settings from JSON file, merging with existing defaults."""
        if os.path.exists(self.settings_file_path):
            try:
                with open(self.settings_file_path, 'r') as f:
                    loaded_settings = json.load(f)
                    self._settings.update(loaded_settings)
            except FileNotFoundError:
                logger.warning(
                    f"Settings file not found: {self.settings_file_path}. Using default settings."
                )
            except json.JSONDecodeError:
                logger.warning(
                    f"Corrupt settings file: {self.settings_file_path}. Using default settings."
                )
            except IOError as e:
                logger.error(f"Error reading settings file {self.settings_file_path}: {e}")
    def _save_settings(self):
        """Save current settings to JSON file."""
        os.makedirs(os.path.dirname(self.settings_file_path), exist_ok=True)
        try:
            with open(self.settings_file_path, 'w') as f:
                json.dump(self._settings, f, indent=4)
        except IOError as e:
            logger.error(f"Error writing settings to file {self.settings_file_path}: {e}")
    def get_setting(self, name, default_value=None):
        """Get a setting value by name.
        Args:
            name: Setting name to retrieve.
            default_value: Value to return if setting not found.
        Returns:
            Setting value or default_value if not found.
        """
        return self._settings.get(name, default_value)
    def set_setting(self, name, value):
        """Set a setting value with validation.
        Args:
            name: Setting name to update.
            value: New value for the setting.
        """
        if self._validate_setting(name, value):
            self._settings[name] = value
            self._save_settings()
            self.settings_changed.emit(name, value)
        else:
            logger.error(
                f"Validation failed for setting '{name}' with value '{value}'. Setting not updated."
            )
    def _validate_setting(self, name, value):
        """Validate a setting value based on its name.
        Args:
            name: Setting name to validate.
            value: Value to validate.
        Returns:
            True if valid, False otherwise.
        """
        if name == "output_bitrate":
            valid_bitrates = [32, 48, 56, 64, 96, 128]
            if not isinstance(value, int) or value not in valid_bitrates:
                logger.warning(f"Invalid output_bitrate: {value}. Must be one of {valid_bitrates}.")
                return False
        elif name == "output_directory":
            if not isinstance(value, str) or not value:
                logger.warning(f"Invalid output_directory: {value}. Must be a non-empty string.")
                return False
        elif name == "output_filename_pattern":
            if not isinstance(value, int) or value not in [0, 1, 2]:
                logger.warning(f"Invalid output_filename_pattern: {value}. Must be 0, 1, or 2.")
                return False
        return True
```

## File: src/abb/ui/widgets/left_panel_widget.py
```python
import logging
from pathlib import Path
from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import QHBoxLayout, QLabel, QListWidget, QPushButton, QVBoxLayout, QWidget
from .dropzone import DropZone
logger = logging.getLogger("AudiobookBoss.LeftPanelWidget")
class LeftPanelWidget(QWidget):
    """Widget for the left panel of the main window.
    Contains a drop zone for files and a list of files.
    """
    # Signals
    files_dropped_signal = Signal(list)
    files_reordered_signal = Signal(list) # New signal for reordered files
    selection_changed_signal = Signal(int)
    request_remove_signal = Signal(int)
    request_move_up_signal = Signal(int)
    request_move_down_signal = Signal(int)
    request_add_files_signal = Signal()
    request_clear_list_signal = Signal()
    def __init__(self, parent=None):
        """Initialize the LeftPanelWidget."""
        super().__init__(parent)
        self._setup_ui()
        self._connect_signals()
    def _setup_ui(self):
        """Set up the UI components."""
        # Create main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        # Create drop zone
        self.drop_zone = DropZone()
        layout.addWidget(self.drop_zone)
        # Create file list widget
        self.file_list_widget = DraggableListWidget(self) # Use custom DraggableListWidget
        self.file_list_widget.setObjectName("file_list_widget")
        layout.addWidget(self.file_list_widget)
        # Create file management buttons
        self._setup_file_management_buttons(layout)
        # Create file properties section
        self._setup_file_properties_ui(layout)
        self.setLayout(layout)
    def _setup_file_management_buttons(self, parent_layout):
        """Set up the file management buttons."""
        # Create a layout for file operations buttons (add/clear)
        file_ops_layout = QHBoxLayout()
        # Create add files button
        self.add_files_button = QPushButton("Add Files")
        self.add_files_button.setObjectName("add_files_button")
        file_ops_layout.addWidget(self.add_files_button)
        # Create clear list button
        self.clear_list_button = QPushButton("Clear List")
        self.clear_list_button.setObjectName("clear_list_button")
        self.clear_list_button.setEnabled(False)  # Disabled by default until list has items
        file_ops_layout.addWidget(self.clear_list_button)
        # Add file operations layout to parent layout
        parent_layout.addLayout(file_ops_layout)
        # Create a layout for selection-based buttons
        buttons_layout = QHBoxLayout()
        # Create move up button
        self.move_up_button = QPushButton("Move Up")
        self.move_up_button.setObjectName("move_up_button")
        self.move_up_button.setEnabled(False)  # Disabled by default until a file is selected
        buttons_layout.addWidget(self.move_up_button)
        # Create move down button
        self.move_down_button = QPushButton("Move Down")
        self.move_down_button.setObjectName("move_down_button")
        self.move_down_button.setEnabled(False)  # Disabled by default until a file is selected
        buttons_layout.addWidget(self.move_down_button)
        # Create remove button
        self.remove_button = QPushButton("Remove")
        self.remove_button.setObjectName("remove_button")
        self.remove_button.setEnabled(False)  # Disabled by default until a file is selected
        buttons_layout.addWidget(self.remove_button)
        # Add buttons layout to parent layout
        parent_layout.addLayout(buttons_layout)
    def _setup_file_properties_ui(self, parent_layout):
        """Set up the file properties UI components."""
        # Create a group for file properties
        properties_layout = QVBoxLayout()
        # Create labels for each property
        # Duration
        duration_layout = QHBoxLayout()
        duration_label = QLabel("Duration:")
        self.duration_value_label = QLabel("")
        self.duration_value_label.setObjectName("duration_value_label")
        duration_layout.addWidget(duration_label)
        duration_layout.addWidget(self.duration_value_label)
        properties_layout.addLayout(duration_layout)
        # Bitrate
        bitrate_layout = QHBoxLayout()
        bitrate_label = QLabel("Bitrate:")
        self.bitrate_value_label = QLabel("")
        self.bitrate_value_label.setObjectName("bitrate_value_label")
        bitrate_layout.addWidget(bitrate_label)
        bitrate_layout.addWidget(self.bitrate_value_label)
        properties_layout.addLayout(bitrate_layout)
        # Sample Rate
        sample_rate_layout = QHBoxLayout()
        sample_rate_label = QLabel("Sample Rate:")
        self.sample_rate_value_label = QLabel("")
        self.sample_rate_value_label.setObjectName("sample_rate_value_label")
        sample_rate_layout.addWidget(sample_rate_label)
        sample_rate_layout.addWidget(self.sample_rate_value_label)
        properties_layout.addLayout(sample_rate_layout)
        # Channels
        channels_layout = QHBoxLayout()
        channels_label = QLabel("Channels:")
        self.channels_value_label = QLabel("")
        self.channels_value_label.setObjectName("channels_value_label")
        channels_layout.addWidget(channels_label)
        channels_layout.addWidget(self.channels_value_label)
        properties_layout.addLayout(channels_layout)
        # Add properties layout to parent layout
        parent_layout.addLayout(properties_layout)
    def _connect_signals(self):
        """Connect signals to slots."""
        # Connect DropZone's filesDropped signal to our handler
        self.drop_zone.filesDropped.connect(self._on_files_dropped)
        # Connect QListWidget's itemSelectionChanged signal to our handler
        self.file_list_widget.itemSelectionChanged.connect(self._on_selection_changed)
        # Connect button clicked signals to our handlers
        self.add_files_button.clicked.connect(self._on_add_files_button_clicked)
        self.clear_list_button.clicked.connect(self._on_clear_list_button_clicked)
        self.remove_button.clicked.connect(self._on_remove_button_clicked)
        self.move_up_button.clicked.connect(self._on_move_up_button_clicked)
        self.move_down_button.clicked.connect(self._on_move_down_button_clicked)
    def _on_files_dropped(self, file_paths):
        """Handle files dropped on the drop zone."""
        logger.info(f"Files dropped: {len(file_paths)} files")
        # Re-emit the signal
        self.files_dropped_signal.emit(file_paths)
    def update_file_list_display(self, file_paths):
        """Update the file list display with the provided file paths.
        Args:
            file_paths (list): List of file paths to display
        """
        # Clear the list widget
        self.file_list_widget.clear()
        # Add each file path to the list widget
        for path in file_paths:
            # Extract just the filename from the path for display
            filename = Path(path).name
            self.file_list_widget.addItem(filename)
        # Enable/disable clear list button based on whether there are items
        self.clear_list_button.setEnabled(len(file_paths) > 0)
        logger.info(f"Updated file list display with {len(file_paths)} files")
    def _on_selection_changed(self):
        """Handle selection changed in the file list widget."""
        # Get the current row index
        current_row = self.file_list_widget.currentRow()
        # Check if there are any items in the list
        has_items = self.file_list_widget.count() > 0
        # Enable/disable the clear list button based on whether there are items
        self.clear_list_button.setEnabled(has_items)
        # Only emit the signal if a valid row is selected
        if current_row >= 0:
            logger.info(f"Selection changed to row {current_row}")
            self.selection_changed_signal.emit(current_row)
            # Enable the buttons when an item is selected
            self.remove_button.setEnabled(True)
            # Enable/disable move up/down buttons based on position
            total_items = self.file_list_widget.count()
            self.move_up_button.setEnabled(current_row > 0)
            self.move_down_button.setEnabled(current_row < total_items - 1)
        else:
            # Disable selection-dependent buttons when no item is selected
            self.remove_button.setEnabled(False)
            self.move_up_button.setEnabled(False)
            self.move_down_button.setEnabled(False)
    def _on_remove_button_clicked(self):
        """Handle remove button clicked."""
        # Get the current row index
        current_row = self.file_list_widget.currentRow()
        # Only emit the signal if a valid row is selected
        if current_row >= 0:
            logger.info(f"Remove button clicked for row {current_row}")
            self.request_remove_signal.emit(current_row)
    def _on_move_up_button_clicked(self):
        """Handle move up button clicked."""
        # Get the current row index
        current_row = self.file_list_widget.currentRow()
        # Only emit the signal if a valid row is selected and it's not the first item
        if current_row > 0:
            logger.info(f"Move up button clicked for row {current_row}")
            self.request_move_up_signal.emit(current_row)
    def _on_move_down_button_clicked(self):
        """Handle move down button clicked."""
        # Get the current row index
        current_row = self.file_list_widget.currentRow()
        total_items = self.file_list_widget.count()
        # Only emit the signal if a valid row is selected and it's not the last item
        if current_row >= 0 and current_row < total_items - 1:
            logger.info(f"Move down button clicked for row {current_row}")
            self.request_move_down_signal.emit(current_row)
    def _on_add_files_button_clicked(self):
        """Handle add files button clicked."""
        logger.info("Add files button clicked")
        self.request_add_files_signal.emit()
    def _on_clear_list_button_clicked(self):
        """Handle clear list button clicked."""
        # Only emit the signal if there are files in the list
        if self.file_list_widget.count() > 0:
            logger.info("Clear list button clicked")
            self.request_clear_list_signal.emit()
    def update_selected_file_properties_display(self, file_properties):
        """Update the file properties display with the provided properties.
        Args:
            file_properties (dict): Dictionary of file properties to display
        """
        # Update each label with the corresponding property value
        if "duration" in file_properties:
            self.duration_value_label.setText(file_properties["duration"])
        if "bitrate" in file_properties:
            self.bitrate_value_label.setText(file_properties["bitrate"])
        if "sample_rate" in file_properties:
            self.sample_rate_value_label.setText(file_properties["sample_rate"])
        if "channels" in file_properties:
            self.channels_value_label.setText(file_properties["channels"])
        logger.info("Updated file properties display")
    def _handle_files_reordered(self):
        """Handles the reordering of files in the list widget and emits the signal.
        This method is called by DraggableListWidget after a drop event.
        """
        new_order_paths = []
        for i in range(self.file_list_widget.count()):
            new_order_paths.append(self.file_list_widget.item(i).text())
        self.files_reordered_signal.emit(new_order_paths)
        logger.info(f"Files reordered. New order: {new_order_paths}")
class DraggableListWidget(QListWidget):
    """Custom QListWidget to handle drag and drop reordering and emit a signal.
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDragDropMode(QListWidget.InternalMove)
        self.setDefaultDropAction(Qt.MoveAction)
        self.setSelectionMode(QListWidget.SingleSelection)
        self.setDragEnabled(True)
        self.setAcceptDrops(True)
    def dropEvent(self, event):
        """Handle the drop event for reordering items.
        """
        super().dropEvent(event)
        # After the superclass handles the drop, notify the parent LeftPanelWidget
        # to emit the reordered signal.
        if isinstance(self.parent(), LeftPanelWidget):
            self.parent()._handle_files_reordered()
```

## File: src/abb/ui/widgets/output_settings_widget.py
```python
from PySide6.QtCore import Signal
from PySide6.QtWidgets import (
    QCheckBox,
    QComboBox,
    QFileDialog,
    QFormLayout,
    QHBoxLayout,
    QLineEdit,
    QPushButton,
    QVBoxLayout,
    QWidget,
)
class OutputSettingsWidget(QWidget):
    setting_changed_signal = Signal(str, object)
    def __init__(self, app_state=None, initial_settings=None, parent=None):
        super().__init__(parent)
        # Support both app_state and initial_settings for backward compatibility
        if app_state is not None and isinstance(app_state, dict):
            self.initial_settings = app_state.get("settings", {})
        else:
            self.initial_settings = initial_settings if initial_settings is not None else {}
        self._init_ui()
        self._load_initial_values()
    def _init_ui(self):
        main_layout = QVBoxLayout(self)
        form_layout = QFormLayout()
        # Bitrate
        self.bitrate_combo = QComboBox()
        self.bitrate_combo.addItems([str(x) for x in [32, 48, 56, 64, 96, 128]])
        form_layout.addRow("Bitrate:", self.bitrate_combo)
        self.bitrate_combo.currentTextChanged.connect(
            lambda text: self._on_setting_changed("output_bitrate", int(text))
        )
        # Sample Rate
        self.samplerate_combo = QComboBox()
        self.samplerate_combo.addItems(["Auto (Pass-through)", "22050", "32000", "44100", "48000"])
        form_layout.addRow("Sample Rate:", self.samplerate_combo)
        self.samplerate_combo.currentTextChanged.connect(self._on_sample_rate_changed)
        # Channels
        self.channels_combo = QComboBox()
        self.channels_combo.addItems(["Mono", "Stereo"])
        form_layout.addRow("Channels:", self.channels_combo)
        self.channels_combo.currentTextChanged.connect(self._on_channels_changed)
        # Output Directory
        output_dir_layout = QHBoxLayout()
        self.output_dir_edit = QLineEdit()
        self.output_dir_button = QPushButton("Browse...")
        output_dir_layout.addWidget(self.output_dir_edit)
        output_dir_layout.addWidget(self.output_dir_button)
        form_layout.addRow("Output Directory:", output_dir_layout)
        self.output_dir_edit.textChanged.connect(
            lambda text: self._on_setting_changed("output_directory", text)
        )
        self.output_dir_button.clicked.connect(self._on_browse_output_dir)
        # Filename Pattern
        self.filename_pattern_edit = QLineEdit()
        form_layout.addRow("Filename Pattern:", self.filename_pattern_edit)
        self.filename_pattern_edit.textChanged.connect(
            lambda text: self._on_setting_changed("output_filename_pattern", text)
        )
        # Create Subdirectory
        self.subdir_checkbox = QCheckBox("Create subdirectory for each audiobook")
        form_layout.addRow("", self.subdir_checkbox)
        self.subdir_checkbox.stateChanged.connect(
            lambda state: self._on_setting_changed("output_create_subdirectory", bool(state))
        )
        main_layout.addLayout(form_layout)
        main_layout.addStretch(1) # Push content to the top
    def _load_initial_values(self):
        # Disconnect signals temporarily to prevent emitting signals during initial load
        self.bitrate_combo.blockSignals(True)
        self.samplerate_combo.blockSignals(True)
        self.channels_combo.blockSignals(True)
        self.output_dir_edit.blockSignals(True)
        self.filename_pattern_edit.blockSignals(True)
        self.subdir_checkbox.blockSignals(True)
        self.bitrate_combo.setCurrentText(str(self.initial_settings.get("output_bitrate", 64)))
        sample_rate_value = self.initial_settings.get("output_sample_rate", "auto")
        # Set the sample rate value
        if sample_rate_value == "auto" or sample_rate_value is None:
            self.samplerate_combo.setCurrentText("Auto (Pass-through)")
        else:
            # Convert to string and set
            rate_str = str(sample_rate_value)
            if rate_str in ["22050", "32000", "44100", "48000"]:
                self.samplerate_combo.setCurrentText(rate_str)
            else:
                # If it's a non-standard rate, add it to the combo
                self.samplerate_combo.addItem(rate_str)
                self.samplerate_combo.setCurrentText(rate_str)
        channels_value = self.initial_settings.get("output_channels", 1)
        self.channels_combo.setCurrentText("Stereo" if channels_value == 2 else "Mono")
        self.output_dir_edit.setText(self.initial_settings.get("output_directory", ""))
        self.filename_pattern_edit.setText(str(self.initial_settings.get("output_filename_pattern", "0")))
        self.subdir_checkbox.setChecked(self.initial_settings.get("output_create_subdirectory", False))
        # Reconnect signals
        self.bitrate_combo.blockSignals(False)
        self.samplerate_combo.blockSignals(False)
        self.channels_combo.blockSignals(False)
        self.output_dir_edit.blockSignals(False)
        self.filename_pattern_edit.blockSignals(False)
        self.subdir_checkbox.blockSignals(False)
    def _on_setting_changed(self, setting_name, new_value):
        if setting_name == "output_filename_pattern":
            try:
                new_value = int(new_value)
            except ValueError:
                # If conversion to int fails, emit the raw string and let SettingsManager handle validation
                pass
        self.setting_changed_signal.emit(setting_name, new_value)
    def _on_channels_changed(self, text):
        int_value = 2 if text == "Stereo" else 1
        self._on_setting_changed("output_channels", int_value)
    def _on_sample_rate_changed(self, text):
        """Handle sample rate combo box changes."""
        if text == "Auto (Pass-through)":
            # Use None or "auto" to indicate pass-through mode
            self._on_setting_changed("output_sample_rate", "auto")
        else:
            # Convert to integer for specific sample rates
            self._on_setting_changed("output_sample_rate", int(text))
    def set_sample_rate(self, sample_rate):
        """Set the sample rate value.
        Args:
            sample_rate: The sample rate in Hz (int) or "auto" for pass-through
        """
        # Block signals to prevent double emission
        self.samplerate_combo.blockSignals(True)
        if sample_rate == "auto" or sample_rate is None:
            self.samplerate_combo.setCurrentText("Auto (Pass-through)")
        else:
            rate_str = str(sample_rate)
            # Check if rate is in combo box
            if rate_str not in [self.samplerate_combo.itemText(i) for i in range(self.samplerate_combo.count())]:
                # Add non-standard sample rate
                self.samplerate_combo.addItem(rate_str)
            self.samplerate_combo.setCurrentText(rate_str)
        self.samplerate_combo.blockSignals(False)
        # Emit the setting changed signal
        self._on_setting_changed("output_sample_rate", sample_rate)
    def _on_browse_output_dir(self):
        selected_dir = QFileDialog.getExistingDirectory(self, "Select Output Directory")
        if selected_dir:
            self.output_dir_edit.setText(selected_dir) # This will trigger textChanged and emit signal
    def populate_settings(self, settings):
        """Populate the output settings with the provided settings.
        Args:
            settings (dict): Dictionary containing settings values
        """
        # Update initial settings and reload values
        self.initial_settings = settings
        self._load_initial_values()
```

## File: src/abb/metadata_utils.py
```python
"""Metadata utilities for Audiobook Boss.
Handles extraction of metadata from audio files using mutagen.
"""
from pathlib import Path
from typing import Any, Dict, Optional
import mutagen
from mutagen.id3 import ID3
from mutagen.mp4 import MP4
from PySide6.QtGui import QImage, QPixmap
_metadata_cache = {}
def extract_metadata(file_path: str) -> Dict[str, Any]:
    """Extract metadata from an audio file.
    Args:
        file_path: Path to the audio file
    Returns:
        Dictionary containing metadata fields
    """
    if file_path in _metadata_cache:
        return _metadata_cache[file_path]
    metadata = {
        "title": "",
        "artist": "",  # Author field in UI, maps to TPE1/©ART
        "album": "",
        "narrator": "",
        "year": "",
        "genre": "",
        "series": "",
        "series_position": "",
        "series_sort": "",
        "description": "",
        "cover_art": None,
    }
    try:
        file_ext = Path(file_path).suffix.lower()
        if file_ext == ".mp3":
            metadata = extract_mp3_metadata(file_path, metadata)
        elif file_ext in (".m4a", ".m4b", ".aac"):
            metadata = extract_mp4_metadata(file_path, metadata)
        metadata = apply_metadata_defaults(metadata)
        _metadata_cache[file_path] = metadata
    except Exception as e:
        print(f"Error extracting metadata from {file_path}: {e}")
    return metadata
def extract_mp3_metadata(file_path: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
    """Extract metadata from an MP3 file using ID3 tags.
    Args:
        file_path: Path to the MP3 file
        metadata: Existing metadata dictionary to update
    Returns:
        Updated metadata dictionary
    """
    try:
        audio = ID3(file_path)
        if "TIT2" in audio:
            metadata["title"] = str(audio["TIT2"])
        if "TPE1" in audio:
            metadata["artist"] = str(audio["TPE1"])
        if "TALB" in audio:
            metadata["album"] = str(audio["TALB"])
        if "TPE2" in audio: # Often Album Artist, used for Narrator here
            metadata["narrator"] = str(audio["TPE2"])
        if "TDRC" in audio: # Year
            metadata["year"] = str(audio["TDRC"]).split("-")[0]
        if "TCON" in audio:
            metadata["genre"] = str(audio["TCON"])
        # Custom/less common tags often used for audiobooks
        if "TXXX:SERIES" in audio:
            metadata["series"] = str(audio["TXXX:SERIES"])
        if "TXXX:SERIES-PART" in audio:
            metadata["series_position"] = str(audio["TXXX:SERIES-PART"])
        if "TXXX:SERIESSORT" in audio:
            metadata["series_sort"] = str(audio["TXXX:SERIESSORT"])
        if "COMM" in audio: # Comments
            metadata["description"] = str(audio["COMM"])
        metadata["cover_art"] = extract_cover_art_from_mp3(audio)
    except Exception as e:
        print(f"Error extracting MP3 metadata: {e}")
    return metadata
def extract_mp4_metadata(file_path: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
    """Extract metadata from an M4A/M4B/AAC file using MP4 tags.
    Args:
        file_path: Path to the M4A/M4B/AAC file
        metadata: Existing metadata dictionary to update
    Returns:
        Updated metadata dictionary
    """
    try:
        audio = MP4(file_path)
        if "©nam" in audio: # Title
            metadata["title"] = str(audio["©nam"][0])
        if "©ART" in audio: # Artist (Author)
            metadata["artist"] = str(audio["©ART"][0])
        if "©alb" in audio: # Album
            metadata["album"] = str(audio["©alb"][0])
        if "aART" in audio: # Album Artist, used for Narrator here
            metadata["narrator"] = str(audio["aART"][0])
        if "©day" in audio: # Year
            year_str = str(audio["©day"][0])
            metadata["year"] = year_str.split("-")[0] if "-" in year_str else year_str
        if "©gen" in audio: # Genre
            metadata["genre"] = str(audio["©gen"][0])
        # MP4 tags often used for audiobook series information
        if "©ser" in audio: # Series
            metadata["series"] = str(audio["©ser"][0])
        if "©pos" in audio: # Series Position (often part of a series)
            metadata["series_position"] = str(audio["©pos"][0])
        if "soaa" in audio: # Sort Album Artist (used for Series Sort here)
            metadata["series_sort"] = str(audio["soaa"][0])
        if "©des" in audio: # Description
            metadata["description"] = str(audio["©des"][0])
        metadata["cover_art"] = extract_cover_art_from_mp4(audio)
    except Exception as e:
        print(f"Error extracting MP4 metadata: {e}")
    return metadata
def extract_cover_art_from_mp3(audio: ID3) -> Optional[QPixmap]:
    """Extract cover art from MP3 ID3 tags.
    Args:
        audio: ID3 object
    Returns:
        QPixmap of cover art or None if not found
    """
    try:
        # Get all APIC frames (cover art)
        apic_frames = audio.getall("APIC:")
        if apic_frames:
            # Take the first APIC frame found
            apic = apic_frames[0]
            img_data = apic.data
            return create_pixmap_from_data(img_data)
    except Exception as e:
        print(f"Error extracting cover art from MP3: {e}")
    return None
def extract_cover_art_from_mp4(audio: MP4) -> Optional[QPixmap]:
    """Extract cover art from MP4 tags.
    Args:
        audio: MP4 object
    Returns:
        QPixmap of cover art or None if not found
    """
    try:
        if "covr" in audio:
            cover_list = audio["covr"]
            if cover_list: # 'covr' is a list of MP4Cover objects
                img_data = cover_list[0] # data is bytes
                return create_pixmap_from_data(img_data)
    except Exception as e:
        print(f"Error extracting cover art from MP4: {e}")
    return None
def create_pixmap_from_data(img_data: bytes) -> Optional[QPixmap]:
    """Create a QPixmap from image data.
    Args:
        img_data: Raw image data bytes
    Returns:
        QPixmap or None if conversion fails
    """
    try:
        pixmap = QPixmap()
        if pixmap.loadFromData(img_data):
            return pixmap
        # Fallback: Try loading via QImage first, then convert to QPixmap
        # This might handle some formats or corrupt data better.
        image = QImage()
        if image.loadFromData(img_data):
            return QPixmap.fromImage(image)
    except Exception as e:
        print(f"Error creating pixmap from image data: {e}")
    return None
def apply_metadata_defaults(metadata: Dict[str, Any]) -> Dict[str, Any]:
    """Apply default logic to metadata.
    Args:
        metadata: Metadata dictionary
    Returns:
        Updated metadata dictionary with defaults applied
    """
    title_value = metadata.get("title")
    if not metadata.get("album") and title_value: # If album is empty, use title
        metadata["album"] = title_value
    series_value = metadata.get("series")
    if not metadata.get("series_sort") and series_value: # If series_sort is empty, use series
        metadata["series_sort"] = series_value
    return metadata
def clear_metadata_cache() -> None:
    """Clear the metadata cache."""
    global _metadata_cache
    _metadata_cache.clear()
def get_duration(file_path: str) -> float:
    """Get the duration of an audio file in seconds.
    Args:
        file_path: Path to the audio file
    Returns:
        Duration in seconds or 0 if not available
    """
    try:
        audio = mutagen.File(file_path)
        if audio and hasattr(audio, "info") and hasattr(audio.info, "length"):
            return audio.info.length
    except Exception as e:
        print(f"Error getting duration: {e}")
    return 0.0
def calculate_estimated_size(duration: float, bitrate: int) -> int:
    """Calculate estimated output file size based on duration and bitrate.
    Args:
        duration: Duration in seconds
        bitrate: Bitrate in kbps
    Returns:
        Estimated size in bytes
    """
    return int(duration * bitrate * 1000 / 8) # size in bytes
def extract_tags(filepath: str) -> dict:
    """Extract common metadata tags from an audio file.
    Uses mutagen to extract Title, Artist, Album, Genre, Track Number, and Year.
    Args:
        filepath: Path to the audio file
    Returns:
        Dictionary with tag names as keys. Missing tags have None as value.
    """
    tags = {
        "title": None,
        "artist": None,
        "album": None,
        "genre": None,
        "track_number": None,
        "year": None
    }
    try:
        audio = mutagen.File(filepath)
        if audio is None:
            return tags
        # Handle different file formats
        file_ext = Path(filepath).suffix.lower()
        if file_ext == ".mp3" and hasattr(audio, "tags"):
            # MP3/ID3 tags
            if "TIT2" in audio.tags:
                tags["title"] = str(audio.tags["TIT2"])
            if "TPE1" in audio.tags:
                tags["artist"] = str(audio.tags["TPE1"])
            if "TALB" in audio.tags:
                tags["album"] = str(audio.tags["TALB"])
            if "TCON" in audio.tags:
                tags["genre"] = str(audio.tags["TCON"])
            if "TRCK" in audio.tags:
                tags["track_number"] = str(audio.tags["TRCK"])
            if "TDRC" in audio.tags:
                tags["year"] = str(audio.tags["TDRC"])
        elif file_ext in (".m4a", ".m4b", ".aac") and hasattr(audio, "tags"):
            # MP4 tags
            if "©nam" in audio.tags:
                tags["title"] = str(audio.tags["©nam"][0])
            if "©ART" in audio.tags:
                tags["artist"] = str(audio.tags["©ART"][0])
            if "©alb" in audio.tags:
                tags["album"] = str(audio.tags["©alb"][0])
            if "©gen" in audio.tags:
                tags["genre"] = str(audio.tags["©gen"][0])
            if "trkn" in audio.tags:
                # Track number is stored as tuple (current, total)
                track_info = audio.tags["trkn"][0]
                if isinstance(track_info, tuple) and len(track_info) > 0:
                    tags["track_number"] = str(track_info[0])
            if "©day" in audio.tags:
                tags["year"] = str(audio.tags["©day"][0])
    except Exception as e:
        print(f"Error extracting tags from {filepath}: {e}")
    return tags
def extract_cover(filepath: str) -> Optional[bytes]:
    """Extract embedded cover art from an audio file.
    Uses mutagen to extract cover art (e.g., APIC frames for MP3).
    Args:
        filepath: Path to the audio file
    Returns:
        Image data as bytes if found, otherwise None
    """
    try:
        audio = mutagen.File(filepath)
        if audio is None:
            return None
        file_ext = Path(filepath).suffix.lower()
        if file_ext == ".mp3":
            # MP3/ID3 - look for APIC frames
            if hasattr(audio, "tags") and audio.tags:
                for key in audio.tags:
                    if key.startswith("APIC:"):
                        apic = audio.tags[key]
                        if hasattr(apic, "data"):
                            return apic.data
        elif file_ext in (".m4a", ".m4b", ".aac"):
            # MP4 - look for covr tag
            if hasattr(audio, "tags") and audio.tags and "covr" in audio.tags:
                cover_list = audio.tags["covr"]
                if cover_list and len(cover_list) > 0:
                    # MP4Cover objects store raw bytes
                    return bytes(cover_list[0])
    except Exception as e:
        print(f"Error extracting cover from {filepath}: {e}")
    return None
```

## File: src/abb/processing_worker.py
```python
"""ProcessingWorker class for Audiobook Boss.
Handles audio processing in a separate thread.
"""
import os
import re
from typing import Dict, List, Optional
from PySide6.QtCore import QObject, QProcess, QProcessEnvironment, QTimer, Signal, Slot
from src.abb.ffmpeg_utils import (
    _get_executable_path,
    build_ffmpeg_command,
    build_ffmpeg_preview_command,
)
class ProcessingWorker(QObject):
    """Worker class for processing audiobooks in a separate thread.
    Inherits from QObject to support signals/slots and moveToThread.
    """
    progress_updated = Signal(int)
    progress = Signal(int)  # For backward compatibility
    status = Signal(str)
    processing_finished = Signal(str) 
    done = Signal()  # For backward compatibility
    processing_error = Signal(str)
    error = Signal(str)  # For backward compatibility
    def __init__(self, ffmpeg_path: str, parent=None) -> None:
        """Initialize the worker.
        Args:
            ffmpeg_path: Path to the FFmpeg executable
            parent: Optional parent QObject
        """
        super().__init__(parent)
        self.ffmpeg_path = ffmpeg_path
        self._process = None
        self._is_processing = False
        self._is_cancelled = False
        self._temp_files = []
        self._output_file = None
        self._kill_timer = None
        self._total_duration_seconds = 0.0
    def process(
        self,
        file_list: List[str],
        output_path: str,
        output_filename: str,
        metadata: Dict,
        settings: Dict,
        total_duration_seconds: float,
    ) -> None:
        """Process the audiobook files.
        Args:
            file_list: List of input audio file paths
            output_path: Directory to save the output file
            output_filename: Name of the output file
            metadata: Dictionary of metadata to embed
            settings: Dictionary of audio settings (bitrate, channels, etc.)
            total_duration_seconds: Total duration of all input files in seconds.
        """
        if self._is_processing:
            self.error.emit("Processing already in progress")
            self.processing_error.emit("Processing already in progress")
            return
        self._is_processing = True
        try:
            self._is_cancelled = False
            self._temp_files = []
            self._total_duration_seconds = total_duration_seconds
            ffmpeg_exe_path = self.ffmpeg_path
            if not ffmpeg_exe_path or not os.path.isfile(ffmpeg_exe_path) or not os.access(ffmpeg_exe_path, os.X_OK):
                self.error.emit("Failed to start: FFmpeg executable not found or not executable.")
                return
            ffprobe_exe_path = _get_executable_path("ffprobe")
            cover_art_temp_path = metadata.get("cover_art_temp_path")
            if cover_art_temp_path and os.path.exists(cover_art_temp_path):
                self._temp_files.append(cover_art_temp_path)
            os.makedirs(output_path, exist_ok=True)
            self._output_file = os.path.join(output_path, output_filename)
            cmd = build_ffmpeg_command(
                input_files=file_list,
                output_file_full_path=self._output_file,
                metadata=metadata,
                settings=settings,
                ffmpeg_exe_path=ffmpeg_exe_path,
                ffprobe_exe_path=ffprobe_exe_path
            )
            if not cmd:
                self.error.emit("Failed to build FFmpeg command. Check input files and settings.")
                self._cleanup_temp_files()
                return
            self._run_process(cmd)
        except Exception as e:
            self._cleanup_temp_files()
            if self._output_file and os.path.exists(self._output_file):
                try:
                    os.remove(self._output_file)
                except OSError:
                    pass # Ignore error on deleting partial file
            self.error.emit(str(e))
            self.processing_error.emit(str(e))
        finally:
            self._is_processing = False
    @Slot()
    def start_full_process(self,
                          file_list: List[str],
                          output_path: str,
                          output_filename: str,
                          metadata: Dict,
                          settings: Dict,
                          total_duration_seconds: float) -> None:
        """Start full audiobook processing.
        This is a slot that can be called from another thread.
        Args:
            file_list: List of input audio file paths
            output_path: Directory to save the output file
            output_filename: Name of the output file
            metadata: Dictionary of metadata to embed
            settings: Dictionary of audio settings (bitrate, channels, etc.)
            total_duration_seconds: Total duration of all input files in seconds
        """
        self.process(file_list, output_path, output_filename, metadata, settings, total_duration_seconds)
    @Slot()
    def start_preview_process(self,
                             input_file_path: str,
                             metadata: Dict,
                             settings: Dict,
                             temp_cover_path: Optional[str] = None,
                             duration_seconds: int = 30) -> None:
        """Start preview processing.
        This is a slot that can be called from another thread.
        Args:
            input_file_path: Path to the input audio file
            metadata: Dictionary of metadata to embed
            settings: Dictionary of audio settings (bitrate, channels, etc.)
            temp_cover_path: Optional path to a cover art image
            duration_seconds: Duration of the preview in seconds (default: 30)
        """
        if self._is_processing:
            self.error.emit("Processing already in progress")
            self.processing_error.emit("Processing already in progress")
            return
        self._is_processing = True
        try:
            self._is_cancelled = False
            self._temp_files = []
            self._total_duration_seconds = duration_seconds
            cmd = build_ffmpeg_preview_command(
                input_file_path=input_file_path,
                metadata=metadata,
                settings=settings,
                temp_cover_path=temp_cover_path,
                duration_seconds=duration_seconds
            )
            if not cmd:
                self.error.emit("Failed to build FFmpeg preview command.")
                self.processing_error.emit("Failed to build FFmpeg preview command.")
                self._is_processing = False
                return
            self._output_file = cmd[-1] # Output file is the last argument
            self._temp_files.append(self._output_file) # Preview output is temporary
            self._run_process(cmd)
        except Exception as e:
            self._cleanup_temp_files()
            if self._output_file and os.path.exists(self._output_file):
                try:
                    os.remove(self._output_file)
                except OSError:
                    pass # Ignore error on deleting partial file
            self.error.emit(str(e))
            self.processing_error.emit(str(e))
        finally:
            self._is_processing = False
    def cancel(self) -> None:
        """Cancel the current processing operation.
        Implements robust cancel logic: terminate → wait 2s → kill.
        """
        if not self._is_processing:
            return
        if not self._process:
            return
        self._is_cancelled = True
        self.status.emit("Cancelling...")
        self._process.terminate()
        self._kill_timer = QTimer()
        self._kill_timer.setSingleShot(True)
        self._kill_timer.timeout.connect(self._force_kill_process)
        self._kill_timer.start(2000)
    def _force_kill_process(self) -> None:
        """Force kill the process if it hasn't terminated after the timeout."""
        if self._process and self._process.state() != QProcess.NotRunning:
            try:
                self.status.emit("Force killing process...")
                self._process.kill()
                self._process.waitForFinished(1000)
            except Exception as e:
                self.status.emit(f"Error force killing process: {str(e)}")
        self._cleanup_after_cancel_or_error()
    def _cleanup_after_cancel_or_error(self) -> None:
        """Clean up after cancellation or error.
        This method handles both cancelled operations and process errors.
        """
        self._cleanup_temp_files()
        if self._output_file and os.path.exists(self._output_file):
            try:
                os.remove(self._output_file)
            except OSError as e:
                self.status.emit(f"Error deleting partial output file: {str(e)}")
        if self._process:
            try:
                self._process.close()
            except Exception as e:
                self.status.emit(f"Error closing process: {str(e)}")
        self.error.emit("Processing cancelled or failed")
        self.processing_error.emit("Processing cancelled or failed")
    def _process_finished(self, exit_code: int, exit_status: QProcess.ExitStatus) -> None:
        """Handle process completion.
        Args:
            exit_code: Process exit code
            exit_status: Process exit status
        """
        try:
            if self._is_cancelled:
                self._cleanup_after_cancel_or_error()
                return
            if exit_status == QProcess.NormalExit and exit_code == 0:
                self.progress.emit(100)
                self.progress_updated.emit(100)
                self.status.emit("Processing completed successfully.")
                self._cleanup_temp_files() # Only non-output temp files
                self.done.emit()
                self.processing_finished.emit(self._output_file or "")
            else:
                error_output = ""
                if self._process:
                    error_output = bytes(self._process.readAllStandardError()).decode("utf-8", errors="replace").strip()
                specific_error_message = f"FFmpeg failed (exit code {exit_code}, status {exit_status.name})"
                if error_output:
                    specific_error_message += f": {error_output}"
                else:
                    specific_error_message += "."
                self.error.emit(specific_error_message)
                self._cleanup_after_cancel_or_error() # Cleans up output file too
        except Exception as e:
            self.error.emit(f"Error handling process completion: {str(e)}")
            self._cleanup_after_cancel_or_error()
        finally:
            self._is_processing = False
            self._is_cancelled = False
    def _cleanup_temp_files(self) -> None:
        """Clean up any temporary files created during processing."""
        for temp_file in self._temp_files:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except OSError:
                    pass
        self._temp_files = []
    def _run_process(self, cmd: List[str]) -> None:
        """Run the FFmpeg process and monitor its progress.
        Args:
            cmd: FFmpeg command as a list of strings
        """
        try:
            self._process = QProcess()
            self._process.setProcessEnvironment(QProcessEnvironment.systemEnvironment())
            self._process.readyReadStandardError.connect(self._read_process_output)
            self._process.finished.connect(self._process_finished)
            self._process.errorOccurred.connect(self._process_error_occurred)
            self.status.emit("Starting processing...")
            if not self._process.start(cmd[0], cmd[1:]):
                self.error.emit(f"Failed to start FFmpeg process: {self._process.errorString()}")
                self._cleanup_after_cancel_or_error()
                return
            if not self._process.waitForStarted(5000):
                self.error.emit(f"FFmpeg process failed to start: {self._process.errorString()}")
                self._cleanup_after_cancel_or_error()
                return
        except Exception as e:
            self.error.emit(f"Error starting FFmpeg process: {str(e)}")
            self._cleanup_after_cancel_or_error()
    def _read_process_output(self) -> None:
        """Read and parse the FFmpeg output to update progress and handle errors."""
        if not self._process:
            return
        try:
            stderr = bytes(self._process.readAllStandardError()).decode("utf-8", errors="replace")
            for line in stderr.splitlines():
                line = line.strip()
                if not line:
                    continue
                # Simple keyword check for errors, might need refinement
                if any(error_keyword in line.lower() for error_keyword in [
                    "error:", "failed:", "cannot", "invalid", "unsupported"
                ]):
                    self.status.emit(f"FFmpeg Potential Error: {line}")
                    # Consider if this should immediately trigger _process_error_occurred
                    # For now, it just emits a status. The exit code will determine final error.
                self.status.emit(line) # Emit raw line for logging/UI display
                progress_info = self._parse_ffmpeg_progress(line)
                if progress_info is not None:
                    self.progress.emit(progress_info)
                    self.progress_updated.emit(progress_info)
        except Exception as e:
            self.error.emit(f"Error processing FFmpeg output: {str(e)}")
            self._cleanup_after_cancel_or_error() # This path might lead to double error signals
            self.status.emit("An error occurred while processing FFmpeg output")
    def _process_error_occurred(self, error: QProcess.ProcessError) -> None:
        """Handle QProcess specific errors."""
        error_map_msg = {
            QProcess.FailedToStart: "Failed to start FFmpeg process",
            QProcess.Crashed: "FFmpeg process crashed",
            QProcess.Timedout: "FFmpeg process timed out",
            QProcess.FailedToStart: "Failed to start FFmpeg process",
            QProcess.Crashed: "FFmpeg process crashed",
            QProcess.Timedout: "FFmpeg process timed out",
            QProcess.WriteError: "Error writing to FFmpeg process",
            QProcess.ReadError: "Error reading from FFmpeg process",
            QProcess.UnknownError: "Unknown FFmpeg error"
        }.get(error, "An unknown QProcess error occurred")
        qprocess_error_string = "N/A"
        if self._process:
            qprocess_error_string = self._process.errorString()
        detailed_error_msg = f"QProcess Error: {error_map_msg}. Details: {qprocess_error_string}"
        self.error.emit(detailed_error_msg)
        self.processing_error.emit(detailed_error_msg)
        self.status.emit(f"Process error: {qprocess_error_string}")
        self._cleanup_after_cancel_or_error()
        # Note: _cleanup_temp_files() is called by _cleanup_after_cancel_or_error()
    def _parse_ffmpeg_progress(self, stderr_line: str) -> Optional[int]:
        """Parse FFmpeg stderr output to extract progress information.
        Args:
            stderr_line: FFmpeg stderr output line
        Returns:
            Progress percentage (0-100) or None if no progress info found
        """
        match = re.search(r"time=(\d{2}):(\d{2}):(\d{2})\.(\d{2})", stderr_line)
        if match:
            hours, minutes, seconds = int(match.group(1)), int(match.group(2)), int(match.group(3))
            # centiseconds = int(match.group(4)) # Not used for percentage
            current_time_seconds = (hours * 3600) + (minutes * 60) + seconds
            if self._total_duration_seconds > 0:
                percentage = (current_time_seconds / self._total_duration_seconds) * 100
                # Clamp to 0-100 as FFmpeg time can sometimes slightly exceed estimated total
                return max(0, min(100, int(percentage)))
            return None # Cannot calculate percentage if total_duration is unknown/zero
        return None
```

## File: src/CLAUDE.local.md
```markdown
# Project Coding & Architecture Guide

This guide provides a unified set of principles and guardrails for the project. Following these rules ensures consistency, maintainability, and architectural integrity.

## 1. Core Principles

- **Orthogonality & Single Responsibility**: Each class or service must have one, and only one, clear responsibility. Modules must communicate only through public service/controller APIs or signals.
- **DRY (Don't Repeat Yourself)**: Extract or reuse code on its 3rd repetition. Prefer shared helper functions and constants over duplicated logic.
- **Simplicity & Readability**: Prefer descriptive names over comments, early returns over nested conditions, and explicit logic over implicit magic.

## 2. Module & Class Structure

- **Atomized Modules (File Size)**: Adhere strictly to these size limits. LOC (Lines of Code) counts only non-empty, non-comment lines.

  | Zone   | LOC Range | Action                                                                                   |
  | ------ | --------- | ---------------------------------------------------------------------------------------- |
  | Green  | <300      | 👍 Optimal size.                                                                          |
  | Yellow | 300-450   | Warn the user and propose a decomposition plan.                                          |
  | Red    | >450      | **STOP generation.** Emit a plan with ≤3 concrete split options and await user approval. |

- **Method & Class Rules**:
  - **Methods**: Aim for <30 lines. Warn on methods >30 lines; require refactoring for methods >50 lines.
  - **Special Case**: `MainWindow` must be kept under 600 LOC during its refactoring.
  - **Naming**: Use `_service` suffix for service classes and `_widget` for UI widgets.

## 3. Critical Implementation Guardrails

These are non-negotiable rules for system stability and consistency.

- **A. Metadata Source**: **MUST NOT** hard-code FFmpeg tag maps. Use `UnifiedMetadataHandler.get_metadata_mapping()` as the single source of truth.
- **B. Path & Filename Logic**: **MUST** use `PathService` for all output path and filename generation. Do not duplicate sanitization logic.
- **C. Processing Safety**: **MUST** call `ProcessingValidator.validate_all()` before launching a full processing job via `ProcessingService.process_full`.
- **D. File I/O Funnel**: **MUST** perform all file list modifications (add, remove, reorder) through `FileService`.
- **E. Settings Persistence**: Output-related keys **MUST** start with `output_*`. Non-output keys (e.g., `last_input_dir`) are permitted for UX state, but managed via `SettingsManager`.
- **F. Feature Flags**: **MUST** respect any `ABB_*` feature flag present in the environment, wrapping new behaviors in a flag check that defaults to legacy behavior.

## 4. Qt-Specific Patterns

- **Threading & Concurrency**:
  - Offload all blocking/heavy operations to a worker using the `QThread` pattern.
  - **Never** start `QProcess` directly from a `QWidget` or `MainWindow`.
  - State flags (`_is_processing`) **MUST** be reset in a `finally` block to guarantee state safety.
  - Temporary files and resources **MUST** be cleaned up unconditionally in a `finally` block or dedicated `_cleanup()` method.

- **Signal Hygiene**:
  - **Max 3 hops** for any end-to-end signal chain.
  - Services **MUST** emit base names (e.g., `metadata_updated`).
  - UI Widgets **MUST** append `_signal` (e.g., `metadata_updated_signal`).
  - Controllers may use free-form names but should prefer the `_signal` suffix for consistency.
  - New `QObject`-based classes should expose **≤ 5** signals.

## 5. Development & Testing Practices

- **Documentation**: Every public class/method requires a concise Google-style docstring explaining the "why," not the "what."
- **Unit Tests**: New code **MUST** include or update a behavior-level pytest.
- **Dependency Injection**: Pass external executables (e.g., `ffmpeg`) as parameters. New code **MUST NOT** perform global lookups for them.
- **Logging**: Use the project's configured logger (`AudiobookBoss.<ModuleName>`). Bare `print()` statements are forbidden.
- **Type Safety**: `typing.Any` **MUST NOT** be used in public-facing function signatures unless technically unavoidable.

## 6. Refactoring

- **Goal Pattern**: Widgets should hold a direct reference to a controller to access services.
- **Anti-Patterns to Avoid**: Signal chains > 3 hops, services inheriting from `QObject`, UI logic in controllers, scattered metadata logic.
- **Safety Checklist**: Before refactoring, ensure: (1) CI tests pass, (2) a "golden run" output is available for regression testing, (3) changes are wrapped in feature flags where appropriate, and (4) pull requests are small and focused (< 400 LOC).
```

## File: src/abb/services/metadata_service.py
```python
"""MetadataService for Audiobook Boss.
Handles metadata extraction, application of defaults, and updates.
"""
from typing import Any, Dict, Optional
from PySide6.QtCore import QObject, Signal
from ..metadata_utils import apply_metadata_defaults, extract_cover, extract_metadata, extract_tags
from .unified_metadata_handler import UnifiedMetadataHandler
class MetadataService(QObject):
    """Service for managing audio metadata.
    Handles operations like extracting metadata from files, applying defaults,
    and updating metadata fields. Emits signals when metadata changes.
    """
    # Phase 1B: Consolidated signals as per spec - exactly 3 signals
    metadata_loaded = Signal(dict)  # For file loading operations
    metadata_updated = Signal(dict)  # For metadata changes and updates  
    metadata_error = Signal(str)    # For error handling
    def __init__(self):
        """Initialize the MetadataService with empty metadata."""
        super().__init__()
        self._metadata = {}
        self.current_metadata: Optional[dict] = None
        self.current_cover_art_data: Optional[bytes] = None
        self.current_cover_art_path: Optional[str] = None
        self._unified_handler = UnifiedMetadataHandler()
    def get_metadata(self) -> Dict[str, Any]:
        """Get a copy of the current metadata.
        Returns:
            Dictionary containing metadata fields
        """
        return self._metadata.copy()
    def extract_metadata(self, file_path: str) -> Dict[str, Any]:
        """Extract metadata from an audio file.
        Args:
            file_path: Path to the audio file
        Returns:
            Dictionary containing metadata fields
        """
        if UnifiedMetadataHandler.is_enabled():
            metadata = self._unified_handler.load_from_file(file_path)
        else:
            metadata = extract_metadata(file_path) # From metadata_utils
        self._metadata = metadata
        self.metadata_loaded.emit(self._metadata.copy())
        return metadata
    def apply_defaults(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Apply default values to metadata.
        Args:
            metadata: Metadata dictionary to apply defaults to
        Returns:
            Updated metadata dictionary with defaults applied
        """
        if UnifiedMetadataHandler.is_enabled():
            # Update handler's state with provided metadata and apply defaults
            self._unified_handler._current_metadata = metadata.copy()
            self._unified_handler.apply_defaults()
            updated_metadata = self._unified_handler.get_current_metadata()
        else:
            updated_metadata = apply_metadata_defaults(metadata) # From metadata_utils
        self._metadata = updated_metadata
        self.metadata_updated.emit(self._metadata.copy())
        return updated_metadata
    def update_metadata_field(self, field: str, value: Any) -> None:
        """Update a metadata field.
        Args:
            field: Name of the field to update
            value: New value for the field
        """
        if self._metadata.get(field) == value: # Handles field not existing too
            return # No change, or field doesn't exist and value is None (or default)
        self._metadata[field] = value
        self.metadata_updated.emit(self._metadata.copy())
    def clear_metadata(self) -> None:
        """Clear all metadata."""
        if not self._metadata: # Already empty
            return
        self._metadata = {}
        self.metadata_updated.emit(self._metadata.copy())
    def extract_and_load_metadata(self, filepath: str) -> None:
        """Extract metadata and cover art from a file and emit signals.
        This method extracts tags and cover art from the given file,
        stores them internally, and emits the appropriate signals.
        Args:
            filepath: Path to the audio file
        """
        if UnifiedMetadataHandler.is_enabled():
            # Extract tags using the unified handler
            self.current_metadata = self._unified_handler.extract_tags_only(filepath)
            # Extract cover art using the unified handler
            self.current_cover_art_data = self._unified_handler.extract_cover_art(filepath)
        else:
            # Extract tags using the original function
            self.current_metadata = extract_tags(filepath)
            # Extract cover art using the original function
            self.current_cover_art_data = extract_cover(filepath)
        # Emit consolidated signal with both metadata and cover art data
        metadata_with_cover = self.current_metadata.copy() if self.current_metadata else {}
        if self.current_cover_art_data is not None:
            metadata_with_cover['cover_art_data'] = self.current_cover_art_data
        self.metadata_loaded.emit(metadata_with_cover)
    def update_current_metadata(self, field_name: str, new_value: str):
        """Update the service's internal current_metadata dictionary and emit a signal.
        Args:
            field_name: The name of the metadata field to update.
            new_value: The new value for the specified field.
        """
        if self.current_metadata is None:
            self.current_metadata = {}
        self.current_metadata[field_name] = new_value
        self.metadata_updated.emit(self.current_metadata)
    def set_cover_art(self, image_path: str):
        """Update the internal current_cover_art_path and emit a signal.
        Args:
            image_path: The path to the new cover art image.
        """
        self.current_cover_art_path = image_path
        # Emit metadata update with cover art path
        if self.current_metadata is None:
            self.current_metadata = {}
        updated_metadata = self.current_metadata.copy()
        updated_metadata['cover_art_path'] = self.current_cover_art_path
        self.metadata_updated.emit(updated_metadata)
    def get_cover_art_path(self) -> Optional[str]:
        """Get the current cover art path.
        Returns:
            Optional[str]: Path to cover art image or None
        """
        return self.current_cover_art_path
```

## File: src/abb/services/processing_service.py
```python
"""ProcessingService for Audiobook Boss.
Manages audio processing operations using QProcess.
"""
import os
from typing import Any, Dict, List, Optional
from PySide6.QtCore import QObject, QThread, Signal
from src.abb.ffmpeg_utils import _get_executable_path
from src.abb.metadata_utils import get_duration
from src.abb.processing_validator import ProcessingValidator
from src.abb.processing_worker import ProcessingWorker
from src.abb.services.path_service import PathService
class ProcessingService(QObject):
    """Service for managing audio processing operations.
    Uses QProcess to run FFmpeg commands for processing audiobooks.
    Provides signals for progress updates, completion, and errors.
    """
    progress = Signal(int)
    finished = Signal(str)
    error = Signal(str)
    status = Signal(str)
    def __init__(self):
        """Initialize the ProcessingService."""
        super().__init__()
        ffmpeg_path = self._get_ffmpeg_path()
        self._worker = ProcessingWorker(ffmpeg_path)
        self._worker_thread = QThread()
        self._validator = ProcessingValidator()
        self._path_service = PathService()
        self._worker.progress_updated.connect(self._on_worker_progress)
        self._worker.status.connect(self._on_worker_status)
        self._worker.processing_finished.connect(self._on_worker_finished)
        self._worker.processing_error.connect(self._on_worker_error)
        self._worker_thread.finished.connect(self._worker.deleteLater)
        self._worker_thread.finished.connect(self._worker_thread.deleteLater)
        self._output_file = "" # Used to hold the output file path for the 'finished' signal if needed.
    def process_full(self,
                    input_files: List[str],
                    output_path: str,
                    output_filename: str,
                    metadata: Dict[str, Any],
                    settings: Dict[str, Any]) -> None:
        """Process a full audiobook.
        Args:
            input_files: List of input audio file paths
            output_path: Directory to save the output file
            output_filename: Name of the output file
            metadata: Dictionary of metadata to embed
            settings: Dictionary of audio settings (bitrate, channels, etc.)
        """
        os.makedirs(output_path, exist_ok=True)
        output_file_full_path = os.path.join(output_path, output_filename)
        self._output_file = output_file_full_path # Storing for potential use, though worker passes path back
        total_duration = sum(get_duration(file_path) for file_path in input_files)
        if not self._worker_thread.isRunning():
            self._worker.moveToThread(self._worker_thread)
            self._worker_thread.start()
        self.status.emit("Starting audio processing...")
        self._worker.start_full_process(
            input_files,
            output_path,
            output_filename,
            metadata,
            settings,
            total_duration
        )
    def start_processing(self,
                        file_list: List[str],
                        output_settings: Dict[str, Any],
                        metadata: Dict[str, Any],
                        cover_art_path: Optional[str] = None) -> None:
        """Start audio processing with validation.
        This is the main entry point for Feature 1.7 that includes:
        - Pre-processing validation
        - Output path generation
        - Metadata preparation
        - Delegating to process_full
        Args:
            file_list: List of input audio file paths
            output_settings: Dictionary containing:
                - output_directory: Base output directory
                - output_filename_pattern: 0 or 1 for filename pattern
                - use_subdirectory_pattern: Whether to create subdirs
                - output_bitrate: Bitrate in kbps
                - output_channels: 1 for mono, 2 for stereo
                - output_sample_rate: Sample rate or None for auto
            metadata: Dictionary of metadata to embed
            cover_art_path: Optional path to cover art image
        """
        from pathlib import Path
        # Convert to Path objects for validation
        input_paths = [Path(f) for f in file_list]
        # Calculate estimated size
        total_duration = sum(get_duration(str(f)) for f in input_paths)
        bitrate_kbps = output_settings.get('output_bitrate', 64)
        estimated_size_mb = (total_duration * bitrate_kbps * 1000 / 8) / (1024 * 1024)
        # Generate output path
        output_dir = self._path_service.calculate_output_path(
            base_directory=output_settings['output_directory'],
            metadata=metadata,
            use_subdirectory=output_settings.get('use_subdirectory_pattern', True)
        )
        # Validate before processing
        valid, error_msg = self._validator.validate_all(
            input_files=input_paths,
            output_directory=Path(output_dir),
            estimated_size_mb=estimated_size_mb
        )
        if not valid:
            self.error.emit(f"Validation failed: {error_msg}")
            self.status.emit(f"Cannot start processing: {error_msg}")
            return
        # Generate filename
        pattern = output_settings.get('output_filename_pattern', 0)
        output_filename = self._path_service.generate_output_filename(
            metadata=metadata,
            pattern=pattern
        )
        # Prepare metadata with cover art
        processing_metadata = metadata.copy()
        if cover_art_path:
            processing_metadata['cover_art_temp_path'] = cover_art_path
        # Prepare audio settings
        audio_settings = {
            'bitrate': output_settings.get('output_bitrate', 64),
            'channels': output_settings.get('output_channels', 1),
        }
        # Only include sample rate if specified
        if output_settings.get('output_sample_rate'):
            audio_settings['sample_rate'] = output_settings['output_sample_rate']
        # Delegate to existing process_full
        self.process_full(
            input_files=file_list,
            output_path=output_dir,
            output_filename=output_filename,
            metadata=processing_metadata,
            settings=audio_settings
        )
    def process_preview(self,
                       input_file_path: str,
                       metadata: Dict[str, Any],
                       settings: Dict[str, Any],
                       temp_cover_path: Optional[str] = None,
                       duration_seconds: int = 30) -> None:
        """Generate a preview of an audiobook.
        Args:
            input_file_path: Path to the input audio file
            metadata: Dictionary of metadata to embed
            settings: Dictionary of audio settings (bitrate, channels, etc.)
            temp_cover_path: Optional path to a cover art image
            duration_seconds: Duration of the preview in seconds (default: 30)
        """
        if not self._worker_thread.isRunning():
            self._worker.moveToThread(self._worker_thread)
            self._worker_thread.start()
        self.status.emit("Generating preview...")
        self._worker.start_preview_process(
            input_file_path,
            metadata,
            settings,
            temp_cover_path,
            duration_seconds
        )
    def cancel(self) -> None:
        """Cancel the current processing operation."""
        self.status.emit("Cancelling processing...")
        self._worker.cancel()
        if self._worker_thread.isRunning():
            self._worker_thread.quit()
            self._worker_thread.wait(5000) 
        self.status.emit("Processing cancelled") # This might be premature if worker is still cleaning up
    def cleanup_worker_resources(self) -> None:
        """Clean up worker thread resources.
        Ensures the worker thread is properly shut down before the service is destroyed.
        This prevents the "QThread: Destroyed while thread is still running" error.
        """
        if hasattr(self, '_worker_thread') and self._worker_thread is not None:
            if self._worker_thread.isRunning():
                self._worker_thread.quit()
                if not self._worker_thread.wait(5000): # Wait up to 5 seconds
                    self._worker_thread.terminate() # Force terminate if not finished
                    self._worker_thread.wait(1000) # Wait after terminate
            self._worker = None # Allow GC
            self._worker_thread = None # Allow GC
    def _get_ffmpeg_path(self) -> str:
        """Get the path to the FFmpeg executable.
        Returns:
            Path to the FFmpeg executable
        """
        ffmpeg_path = _get_executable_path("ffmpeg")
        return str(ffmpeg_path) if ffmpeg_path else "ffmpeg" # Default to "ffmpeg" relies on PATH
    # Slots for worker signals
    def _on_worker_progress(self, percentage: int) -> None:
        """Relay progress updates from the worker."""
        self.progress.emit(percentage)
    def _on_worker_status(self, message: str) -> None:
        """Relay status updates from the worker."""
        self.status.emit(message)
    def _on_worker_finished(self, output_path: str) -> None:
        """Relay successful completion from the worker."""
        self.status.emit("Processing completed successfully")
        self.finished.emit(output_path)
    def _on_worker_error(self, error_message: str) -> None:
        """Relay errors from the worker."""
        self.status.emit(f"Processing failed: {error_message}")
        self.error.emit(error_message)
```

## File: src/abb/ui/widgets/metadata_form_widget.py
```python
import logging
from PySide6.QtCore import Signal
from PySide6.QtWidgets import QFormLayout, QHBoxLayout, QLineEdit, QWidget
from .coverart import CoverArtWidget
logger = logging.getLogger("AudiobookBoss.MetadataFormWidget")
class MetadataFormWidget(QWidget):
    """Widget for editing metadata of audio files.
    Contains form fields for various metadata properties and a cover art widget.
    """
    # Signals
    metadata_field_changed = Signal(str, str)  # field_key, new_value
    cover_art_changed_signal = Signal(str)  # new_image_path
    def __init__(self, parent=None):
        """Initialize the MetadataFormWidget."""
        super().__init__(parent)
        self._setup_ui()
        self._connect_signals()
    def _setup_ui(self):
        """Set up the UI components."""
        # Create main layout
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        # Create cover art widget
        self.cover_art = CoverArtWidget()
        main_layout.addWidget(self.cover_art)
        # Create form layout for metadata fields
        form_layout = QFormLayout()
        # Create metadata fields
        self.title_edit = QLineEdit()
        self.title_edit.setObjectName("title_edit")
        form_layout.addRow("Title:", self.title_edit)
        self.artist_edit = QLineEdit()
        self.artist_edit.setObjectName("artist_edit")
        form_layout.addRow("Artist:", self.artist_edit)
        self.author_edit = QLineEdit()
        self.author_edit.setObjectName("author_edit")
        form_layout.addRow("Author:", self.author_edit)
        self.album_edit = QLineEdit()
        self.album_edit.setObjectName("album_edit")
        form_layout.addRow("Album:", self.album_edit)
        self.narrator_edit = QLineEdit()
        self.narrator_edit.setObjectName("narrator_edit")
        form_layout.addRow("Narrator:", self.narrator_edit)
        self.series_edit = QLineEdit()
        self.series_edit.setObjectName("series_edit")
        form_layout.addRow("Series:", self.series_edit)
        self.series_index_edit = QLineEdit()
        self.series_index_edit.setObjectName("series_index_edit")
        form_layout.addRow("Series Index:", self.series_index_edit)
        self.track_number_edit = QLineEdit()
        self.track_number_edit.setObjectName("track_number_edit")
        form_layout.addRow("Track Number:", self.track_number_edit)
        self.year_edit = QLineEdit()
        self.year_edit.setObjectName("year_edit")
        form_layout.addRow("Year:", self.year_edit)
        self.genre_edit = QLineEdit()
        self.genre_edit.setObjectName("genre_edit")
        form_layout.addRow("Genre:", self.genre_edit)
        self.description_edit = QLineEdit()
        self.description_edit.setObjectName("description_edit")
        form_layout.addRow("Description:", self.description_edit)
        # Add form layout to main layout
        main_layout.addLayout(form_layout)
        self.setLayout(main_layout)
    def populate_fields(self, metadata):
        """Populate the form fields with the provided metadata.
        Args:
            metadata (dict): Dictionary containing metadata values
        """
        # Set text fields
        if "title" in metadata:
            self.title_edit.setText(metadata["title"])
        if "artist" in metadata:
            self.artist_edit.setText(metadata["artist"])
        if "author" in metadata:
            self.author_edit.setText(metadata["author"])
        if "album" in metadata:
            self.album_edit.setText(metadata["album"])
        if "narrator" in metadata:
            self.narrator_edit.setText(metadata["narrator"])
        if "series" in metadata:
            self.series_edit.setText(metadata["series"])
        if "series_index" in metadata:
            self.series_index_edit.setText(metadata["series_index"])
        if "track" in metadata:
            self.track_number_edit.setText(metadata["track"])
        if "year" in metadata:
            self.year_edit.setText(metadata["year"])
        if "genre" in metadata:
            self.genre_edit.setText(metadata["genre"])
        if "description" in metadata:
            self.description_edit.setText(metadata["description"])
        # Set cover art if provided
        if "cover_art" in metadata and metadata["cover_art"]:
            self.cover_art.set_cover_art_from_pixmap(metadata["cover_art"])
        logger.info("Populated metadata form fields")
    def get_form_data(self):
        """Get the values from the form fields as a dictionary.
        Returns:
            dict: Dictionary containing the form field values
        """
        # Create a dictionary with the form field values
        form_data = {
            "title": self.title_edit.text(),
            "artist": self.artist_edit.text(),
            "author": self.author_edit.text(),
            "album": self.album_edit.text(),
            "narrator": self.narrator_edit.text(),
            "series": self.series_edit.text(),
            "series_index": self.series_index_edit.text(),
            "track": self.track_number_edit.text(),
            "year": self.year_edit.text(),
            "genre": self.genre_edit.text(),
            "description": self.description_edit.text(),
            "cover_art": self.cover_art.get_cover_art()
        }
        logger.info("Retrieved metadata form field values")
        return form_data
    def _connect_signals(self):
        """Connect signals to slots."""
        # Connect form field textChanged signals to handler
        self.title_edit.editingFinished.connect(lambda: self._on_field_changed("title", self.title_edit.text()))
        self.artist_edit.editingFinished.connect(lambda: self._on_field_changed("artist", self.artist_edit.text()))
        self.author_edit.editingFinished.connect(lambda: self._on_field_changed("author", self.author_edit.text()))
        self.album_edit.editingFinished.connect(lambda: self._on_field_changed("album", self.album_edit.text()))
        self.narrator_edit.editingFinished.connect(lambda: self._on_field_changed("narrator", self.narrator_edit.text()))
        self.series_edit.editingFinished.connect(lambda: self._on_field_changed("series", self.series_edit.text()))
        self.series_index_edit.editingFinished.connect(lambda: self._on_field_changed("series_index", self.series_index_edit.text()))
        self.track_number_edit.editingFinished.connect(lambda: self._on_field_changed("track", self.track_number_edit.text()))
        self.year_edit.editingFinished.connect(lambda: self._on_field_changed("year", self.year_edit.text()))
        self.genre_edit.editingFinished.connect(lambda: self._on_field_changed("genre", self.genre_edit.text()))
        self.description_edit.editingFinished.connect(lambda: self._on_field_changed("description", self.description_edit.text()))
        # Connect cover art changed signals from CoverArtWidget
        self.cover_art.coverArtChanged.connect(self._on_cover_art_changed)
        self.cover_art.cover_art_path_changed.connect(self._on_cover_art_path_changed)
    def _on_field_changed(self, field_key, new_value):
        """Handle field value changed.
        Args:
            field_key (str): Key of the field that changed
            new_value (str): New value of the field
        """
        logger.info(f"Metadata field '{field_key}' changed to '{new_value}'")
        self.metadata_field_changed.emit(field_key, new_value)
    def _on_cover_art_changed(self, pixmap):
        """Handle cover art changed.
        Args:
            pixmap (QPixmap): New cover art pixmap
        """
        logger.info("Cover art pixmap changed")
    def _on_cover_art_path_changed(self, file_path):
        """Handle cover art path changed.
        Args:
            file_path (str): Path to the new cover art file
        """
        logger.info(f"Cover art path changed to: {file_path}")
        # Emit the cover_art_changed_signal with the file path
        self.cover_art_changed_signal.emit(file_path)
    def update_metadata_display(self, metadata_dict: dict):
        """Update the metadata form fields with values from the provided dictionary.
        This slot is connected to MainController.metadata_updated_signal and
        populates the form fields using the metadata dictionary.
        Args:
            metadata_dict: Dictionary containing metadata fields
        """
        # Update each field, using get() to handle missing tags gracefully
        self.title_edit.setText(metadata_dict.get('title', ''))
        self.artist_edit.setText(metadata_dict.get('artist', ''))
        self.album_edit.setText(metadata_dict.get('album', ''))
        self.genre_edit.setText(metadata_dict.get('genre', ''))
        self.track_number_edit.setText(metadata_dict.get('track', ''))
        self.year_edit.setText(metadata_dict.get('date', ''))
        # Also update author field if artist is provided (for compatibility)
        if 'artist' in metadata_dict:
            self.author_edit.setText(metadata_dict.get('artist', ''))
        logger.info("Updated metadata display from signal")
```

## File: src/abb/ui/widgets/right_panel_widget.py
```python
import logging
from PySide6.QtCore import Signal
from PySide6.QtWidgets import (
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QProgressBar,
    QPushButton,
    QVBoxLayout,
    QWidget,
)
from .metadata_form_widget import MetadataFormWidget
from .output_settings_widget import OutputSettingsWidget
logger = logging.getLogger("AudiobookBoss.RightPanelWidget")
class RightPanelWidget(QWidget):
    """Widget for the right panel of the main window.
    Contains metadata form, output settings, and processing controls.
    """
    # Signals
    metadata_field_changed = Signal(str, str)  # field_key, new_value
    setting_changed = Signal(str, object)  # setting_key, new_value
    start_processing_requested = Signal()
    cancel_processing_requested = Signal()
    cover_art_changed_signal = Signal(str)  # image_path
    def __init__(self, main_controller, parent=None):
        """Initialize the RightPanelWidget."""
        super().__init__(parent)
        self.main_controller = main_controller
        self._setup_ui()
        self._connect_signals()
    def _setup_ui(self):
        """Set up the UI components."""
        # Create main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(12)
        # Add panel title
        title_label = QLabel("Metadata & Output")
        title_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        layout.addWidget(title_label)
        # Create metadata section
        self._setup_metadata_section(layout)
        # Create output settings section
        self._setup_output_section(layout)
        # Create processing controls
        self._setup_processing_controls(layout)
        self.setLayout(layout)
    def _setup_metadata_section(self, parent_layout):
        """Set up the metadata section using MetadataFormWidget."""
        # Create metadata group box
        metadata_group = QGroupBox("Metadata")
        metadata_layout = QVBoxLayout(metadata_group)
        # Create MetadataFormWidget
        self.metadata_form_widget = MetadataFormWidget()
        # Add to layout
        metadata_layout.addWidget(self.metadata_form_widget)
        # Add metadata group to parent layout
        parent_layout.addWidget(metadata_group)
    def _setup_output_section(self, parent_layout):
        """Set up the output settings section using OutputSettingsWidget."""
        # Create output group box
        output_group = QGroupBox("Output Settings")
        output_layout = QVBoxLayout(output_group)
        # Create OutputSettingsWidget
        self.output_settings_widget = OutputSettingsWidget()
        # Add to layout
        output_layout.addWidget(self.output_settings_widget)
        # Add output group to parent layout
        parent_layout.addWidget(output_group)
    def _setup_processing_controls(self, parent_layout):
        """Set up the processing controls (progress bar, process/cancel buttons)."""
        # Add estimated size section
        size_layout = QHBoxLayout()
        size_layout.addWidget(QLabel("Estimated Output Size:"))
        self.estimated_size_label = QLabel("—")
        self.estimated_size_label.setStyleSheet(
            "font-family: 'Menlo', 'Monaco', 'Consolas', 'DejaVu Sans Mono', monospace;"
        )
        size_layout.addWidget(self.estimated_size_label)
        size_layout.addStretch(1)
        parent_layout.addLayout(size_layout)
        # Add progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("progress_bar")
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(False)  # Initially hidden
        parent_layout.addWidget(self.progress_bar)
        # Add Process button
        process_layout = QHBoxLayout()
        self.process_button = QPushButton("Process Audiobook")
        self.process_button.setObjectName("process_button")
        self.process_button.setMinimumHeight(40)  # Make button more prominent
        self.cancel_button = QPushButton("Cancel Processing")
        self.cancel_button.setObjectName("cancel_button")
        self.cancel_button.setMinimumHeight(40)
        self.cancel_button.setEnabled(False)  # Initially disabled
        process_layout.addStretch(1)
        process_layout.addWidget(self.process_button)
        process_layout.addWidget(self.cancel_button)
        process_layout.addStretch(1)
        parent_layout.addLayout(process_layout)
    def _connect_signals(self):
        """Connect signals to slots."""
        # Connect MetadataFormWidget signals
        self.metadata_form_widget.metadata_field_changed.connect(self._forward_metadata_field_changed)
        self.metadata_form_widget.cover_art_changed_signal.connect(self._forward_cover_art_changed)
        # Connect RightPanelWidget signals to MainController
        self.metadata_field_changed.connect(self.main_controller.update_metadata_field)
        self.cover_art_changed_signal.connect(self.main_controller.update_cover_art)
        # Connect OutputSettingsWidget signals
        self.output_settings_widget.setting_changed_signal.connect(self._forward_setting_changed)
        # Note: OutputSettingsWidget handles browse directory internally
        # Connect button signals
        self.process_button.clicked.connect(self._on_process_button_clicked)
        self.cancel_button.clicked.connect(self._on_cancel_button_clicked)
    def _forward_metadata_field_changed(self, field_key, new_value):
        """Forward metadata_field_changed signal from MetadataFormWidget."""
        logger.info(f"Forwarding metadata field changed: {field_key} = {new_value}")
        self.metadata_field_changed.emit(field_key, new_value)
    def _forward_setting_changed(self, setting_key, new_value):
        """Forward setting_changed signal from OutputSettingsWidget."""
        logger.info(f"Forwarding setting changed: {setting_key} = {new_value}")
        self.setting_changed.emit(setting_key, new_value)
    # _forward_browse_requested method removed as OutputSettingsWidget handles browse internally
    def _forward_cover_art_changed(self, image_path: str):
        """Forward cover_art_changed_signal from MetadataFormWidget."""
        logger.info(f"Forwarding cover art changed: {image_path}")
        self.cover_art_changed_signal.emit(image_path)
    def _on_process_button_clicked(self):
        """Handle process button clicked."""
        logger.info("Process button clicked")
        self.start_processing_requested.emit()
    def _on_cancel_button_clicked(self):
        """Handle cancel button clicked."""
        logger.info("Cancel button clicked")
        self.cancel_processing_requested.emit()
    def update_progress(self, value):
        """Update the progress bar value and make it visible.
        Args:
            value (int): Progress value (0-100)
        """
        self.progress_bar.setValue(value)
        self.progress_bar.setVisible(True)
        logger.info(f"Progress updated to {value}%")
    def reset_progress(self):
        """Reset the progress bar to 0 and hide it."""
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(False)
        logger.info("Progress reset")
    def set_processing_state(self, is_processing):
        """Set the processing state, updating button enabled states.
        Args:
            is_processing (bool): True if processing is active, False otherwise
        """
        self.process_button.setEnabled(not is_processing)
        self.cancel_button.setEnabled(is_processing)
        logger.info(f"Processing state set to {is_processing}")
    def set_estimated_size(self, size_text):
        """Set the estimated output size label text.
        Args:
            size_text (str): Size text to display
        """
        self.estimated_size_label.setText(size_text)
        logger.info(f"Estimated size set to {size_text}")
    def populate_metadata(self, metadata):
        """Populate the metadata form with the provided metadata.
        Args:
            metadata (dict): Dictionary containing metadata values
        """
        self.metadata_form_widget.populate_fields(metadata)
        logger.info("Metadata populated")
    def get_metadata(self):
        """Get the metadata from the form.
        Returns:
            dict: Dictionary containing the metadata
        """
        return self.metadata_form_widget.get_form_data()
    def populate_settings(self, settings):
        """Populate the output settings with the provided settings.
        Args:
            settings (dict): Dictionary containing settings values
        """
        self.output_settings_widget.populate_settings(settings)
        logger.info("Settings populated")
    def get_settings(self):
        """Get the settings from the output settings widget.
        Returns:
            dict: Dictionary containing the settings
        """
        return self.output_settings_widget.get_settings_data()
    def set_sample_rate(self, sample_rate):
        """Set the sample rate in the output settings widget.
        Args:
            sample_rate (str): Sample rate to display
        """
        self.output_settings_widget.set_sample_rate(sample_rate)
    def set_use_subdirectory(self, checked):
        """Set the subdirectory checkbox state.
        Args:
            checked (bool): Whether to use subdirectory
        """
        if hasattr(self.output_settings_widget, 'use_subdir_checkbox'):
            self.output_settings_widget.use_subdir_checkbox.setChecked(checked)
        else:
            # Handle the case where the checkbox might have a different name
            self.output_settings_widget.subdir_checkbox.setChecked(checked)
    def set_output_directory(self, directory):
        """Set the output directory in the output settings widget.
        Args:
            directory (str): Path to the output directory
        """
        self.output_settings_widget.output_dir_edit.setText(directory)
    def set_metadata_form_enabled(self, enabled):
        """Enable or disable the metadata form widget.
        Args:
            enabled (bool): Whether to enable the widget
        """
        self.metadata_form_widget.setEnabled(enabled)
    def set_output_settings_enabled(self, enabled):
        """Enable or disable the output settings widget.
        Args:
            enabled (bool): Whether to enable the widget
        """
        self.output_settings_widget.setEnabled(enabled)
```

## File: src/abb/ffmpeg_utils.py
```python
"""FFmpeg utilities for Audiobook Boss.
Handles bundled FFmpeg binaries with libfdk support.
"""
import json
import os
import shutil
import subprocess
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from .ffmpeg.command_builder import FFmpegCommandBuilder
_audio_properties_cache: Dict[str, Dict] = {}
def calculate_file_size(file_path: str) -> int:
    """Calculate the size of a file in bytes."""
    if file_path is None:
        return 0
    try:
        return Path(file_path).stat().st_size
    except (FileNotFoundError, OSError, TypeError):
        return 0
def get_audio_properties(file_path: str) -> Tuple[Dict, Optional[str]]:
    """Get audio properties of a file using ffprobe.
    Caches results per file path to avoid repeated calls.
    Returns a tuple: (properties_dict, error_message_or_none)
    - properties_dict: Dictionary with bitrate, sample_rate, channels, file_size.
                     Contains default values if ffprobe fails for some properties.
    - error_message_or_none: String with error details if ffprobe fails, else None.
    """
    global _audio_properties_cache
    error_message: Optional[str] = None
    if file_path in _audio_properties_cache:
        cached_data = _audio_properties_cache.get(file_path)
        if cached_data:
            return cached_data, None
    properties = {
        "bitrate": 0,
        "sample_rate": 0,
        "channels": 0,
        "file_size": calculate_file_size(file_path),
    }
    try:
        ffprobe_exe_path = _get_executable_path("ffprobe")
        if not ffprobe_exe_path:
            error_message = (
                "ffprobe executable not found in bundled Resources/bin or system PATH."
            )
            return properties, error_message
        cmd = [
            str(ffprobe_exe_path),
            "-v",
            "error",
            "-select_streams",
            "a:0",
            "-show_entries",
            "stream=bit_rate,sample_rate,channels",
            "-of",
            "json",
            file_path,
        ]
        result = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=False,
        )
        if result.returncode != 0:
            error_message = f"ffprobe failed with exit code {result.returncode}. Error: {result.stderr.strip()}"
            _audio_properties_cache[file_path] = properties
            return properties, error_message
        data = json.loads(result.stdout)
        if "streams" in data and len(data["streams"]) > 0:
            stream = data["streams"][0]
            if "bit_rate" in stream:
                try:
                    properties["bitrate"] = int(stream["bit_rate"]) // 1000
                except (ValueError, TypeError):
                    pass
            if "sample_rate" in stream:
                try:
                    properties["sample_rate"] = int(stream["sample_rate"])
                except (ValueError, TypeError):
                    pass
            if "channels" in stream:
                properties["channels"] = stream["channels"]
    except json.JSONDecodeError as e:
        error_message = f"Failed to parse ffprobe JSON output: {e}. stdout: {result.stdout.strip() if 'result' in locals() else 'N/A'}"
    except FileNotFoundError:
        error_message = f"Audio file not found: {file_path}"
    except subprocess.SubprocessError as e:
        error_message = f"Subprocess error when running ffprobe: {e}"
    except Exception as e:
        error_message = (
            f"Unexpected error getting audio properties for {file_path}: {e}"
        )
    if not error_message:
        warn_props = []
        for key in ("bitrate", "sample_rate", "channels"):
            if not properties[key]:
                warn_props.append(key)
        # if warn_props: # Intentionally commented out print, will be removed by formatter
            # print(f"[AUDIO PROPERTY WARNING] File {file_path} missing or invalid values for: {', '.join(warn_props)}")
        _audio_properties_cache[file_path] = properties
    return properties, error_message
def _get_resources_bin_dir() -> Path:
    """Determines the path to the bundled 'bin' directory within 'Resources'."""
    if getattr(sys, "frozen", False) and hasattr(sys, "_MEIPASS"):
        base_path = Path(sys._MEIPASS)
    else:
        # In development mode, go up from src/abb to project root
        base_path = Path(__file__).resolve().parent.parent.parent
    return base_path / "Resources" / "bin"
def _get_executable_path(executable_name: str) -> Optional[Path]:
    """Gets the path to an executable, checking bundled, then system PATH."""
    bundled_bin_dir = _get_resources_bin_dir()
    bundled_exe_path = bundled_bin_dir / executable_name
    if bundled_exe_path.exists() and bundled_exe_path.is_file():
        return bundled_exe_path
    system_exe_path_str = shutil.which(executable_name)
    if system_exe_path_str:
        return Path(system_exe_path_str)
    return None
def get_ffmpeg_path() -> Path:
    """Return the path to the bundled FFmpeg binaries."""
    return _get_resources_bin_dir()
def setup_ffmpeg_path() -> None:
    """Add bundled FFmpeg to PATH at runtime."""
    ffmpeg_path = get_ffmpeg_path()
    if not ffmpeg_path.exists():
        raise FileNotFoundError(f"Bundled FFmpeg not found at {ffmpeg_path}")
    os.environ["PATH"] = f"{ffmpeg_path}{os.pathsep}{os.environ.get('PATH', '')}"
def check_ffmpeg_version() -> Optional[str]:
    """Check FFmpeg version and availability."""
    try:
        ffmpeg_exe_path = _get_executable_path("ffmpeg")
        if not ffmpeg_exe_path:
            return None
        cmd = [str(ffmpeg_exe_path), "-version"]
        result = subprocess.run(
            cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True, text=True
        )
        return result.stdout.strip().split("\n")[0]
    except (subprocess.SubprocessError, FileNotFoundError):
        return None
def codec_available(codec_name: str) -> bool:
    """Check if a specific codec is available in the FFmpeg build."""
    try:
        ffmpeg_exe_path = _get_executable_path("ffmpeg")
        if not ffmpeg_exe_path:
            return False
        cmd = [str(ffmpeg_exe_path), "-codecs"]
        result = subprocess.run(
            cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True, text=True
        )
        return codec_name in result.stdout
    except (subprocess.SubprocessError, FileNotFoundError):
        return False
_command_builder = FFmpegCommandBuilder(codec_available)
def build_ffmpeg_preview_command(
    input_file_path: str,
    metadata: Dict[str, Any],
    settings: Dict[str, Any],
    temp_cover_path: Optional[str] = None,
    duration_seconds: int = 30,
) -> List[str]:
    return _command_builder.build_ffmpeg_preview_command(
        input_file_path, metadata, settings,
        temp_cover_path, duration_seconds
    )
def build_ffmpeg_command(
    input_files: List[str],
    output_file_full_path: str,
    metadata: Dict[str, Any],
    settings: Dict[str, Any],
    ffmpeg_exe_path: str,
    ffprobe_exe_path: Optional[str] = None,
) -> List[str]:
    return _command_builder.build_ffmpeg_command(
        input_files, output_file_full_path, metadata,
        settings, ffmpeg_exe_path, ffprobe_exe_path
    )
def build_ffmpeg_processing_command(
    file_list: List[str],
    output_path: str,
    metadata_dict: Dict[str, Any],
    cover_art_path: Optional[str],
    audio_settings_dict: Dict[str, Any],
    ffmpeg_path: str
) -> List[str]:
    """Build FFmpeg command for processing multiple audio files into single M4B.
    Args:
        file_list: List of input audio file paths
        output_path: Full path for output M4B file
        metadata_dict: Metadata to embed (title, artist, etc.)
        cover_art_path: Path to cover art image file
        audio_settings_dict: Audio settings (bitrate, channels, sample_rate)
        ffmpeg_path: Path to FFmpeg executable
    Returns:
        List of command arguments for subprocess
    """
    # Prepare metadata with cover art path
    metadata = metadata_dict.copy()
    if cover_art_path:
        metadata['cover_art_temp_path'] = cover_art_path
    # Build and return command
    return build_ffmpeg_command(
        input_files=file_list,
        output_file_full_path=output_path,
        metadata=metadata,
        settings=audio_settings_dict,
        ffmpeg_exe_path=ffmpeg_path
    )
```

## File: src/abb/ui/widgets/coverart.py
```python
"""CoverArtWidget for Audiobook Boss.
Provides a drag-and-drop area for cover art images with click-to-select functionality.
"""
from pathlib import Path
from typing import Optional
from PySide6.QtCore import QSettings, Qt, Signal
from PySide6.QtGui import (
    QDragEnterEvent,
    QDropEvent,
    QMouseEvent,
    QPixmap,
)
from PySide6.QtWidgets import (
    QFileDialog,
    QLabel,
    QPushButton,
    QVBoxLayout,
    QWidget,
)
MAX_PREVIEW_WIDTH = 180
MAX_PREVIEW_HEIGHT = 180
DEFAULT_PREVIEW_WIDTH = 100
DEFAULT_PREVIEW_HEIGHT = 100
class CoverArtWidget(QWidget):
    """Widget for displaying and managing cover art.
    Accepts drag-drop of image files and provides a button to load images.
    """
    # Signals emitted when cover art is changed
    coverArtChanged = Signal(QPixmap)
    # Additional signal that includes the image path for better integration
    cover_art_path_changed = Signal(str)
    # Supported file extensions
    SUPPORTED_EXTENSIONS = {".jpg", ".jpeg", ".png", ".tiff", ".tif"}
    def __init__(self, parent=None) -> None:
        """Initialize the CoverArtWidget."""
        super().__init__(parent)
        # Create layout
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(8)
        self.setAcceptDrops(True) # Enable drag and drop for the widget itself
        # Create image display label
        self.image_label = QLabel("Drag Image Here")
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setFixedSize(DEFAULT_PREVIEW_WIDTH, DEFAULT_PREVIEW_HEIGHT)
        self.image_label.setAutoFillBackground(True)  # Ensure background is painted
        self.image_label.setStyleSheet(
            """
            QLabel {
                border: 1px solid #505050;  /* Darker, solid border */
                border-radius: 6px; 
                color: #6b7280; /* Text color for 'Drag Image Here' */
                font-size: 13px; 
                background-color: #404040; /* Dark gray background */
            }
            """
        )
        self.image_label.setAcceptDrops(True)
        # Create load button
        self.load_button = QPushButton("Load Cover Art")
        # Add widgets to layout
        self.layout.addWidget(self.image_label)
        self.layout.addWidget(self.load_button)
        # Connect signals
        self.load_button.clicked.connect(self.load_cover_art)
        # Settings for remembering last directory
        self.settings = QSettings("AudiobookBoss", "ABB")
        # Current pixmap
        self.current_pixmap = None
    def load_cover_art(self) -> None:
        """Open file dialog to select cover art image."""
        # Get the last used directory or default to home
        last_dir = self.settings.value("last_cover_dir", str(Path.home()), type=str)
        # Open file dialog
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Cover Art Image",
            last_dir,
            "Image Files (*.jpg *.jpeg *.png *.tiff *.tif)",
        )
        if file_path:
            # Save the directory for next time
            new_dir = str(Path(file_path).parent)
            self.settings.setValue("last_cover_dir", new_dir)
            # Set the cover art
            self.set_cover_art(file_path)
    def set_cover_art(self, file_path: str) -> None:
        """Set the cover art from a file path."""
        # Add null check to prevent TypeError
        if file_path is None:
            print("Error loading cover art: file_path is None")
            return
        try:
            print(f"Attempting to load cover art from: {file_path}")
            # Check file extension
            file_ext = Path(file_path).suffix.lower()
            print(f"File extension: {file_ext}")
            if file_ext not in [".jpg", ".jpeg", ".png", ".tiff", ".tif"]:
                self.image_label.setText("Unsupported format\nOnly JPG, PNG, TIFF")
                return
            # Check file size (8 MB limit)
            file_size_mb = Path(file_path).stat().st_size / (1024 * 1024)
            print(f"File size: {file_size_mb:.2f} MB")
            if file_size_mb > 8:
                self.image_label.setText("File too large\nMax 8MB")
                return
            # Load the image
            print("Loading image into QPixmap...")
            original_pixmap = QPixmap(file_path)
            if original_pixmap.isNull():
                self.image_label.setText("Failed to load image")
                return
            print(
                f"Image loaded successfully. Dimensions: {original_pixmap.width()}x{original_pixmap.height()}"
            )
            # Check original dimensions before any transformation
            width = original_pixmap.width()
            height = original_pixmap.height()
            # Log aspect ratio information for original image
            if width != height:
                print(f"Warning: Image is not 1:1 aspect ratio ({width}x{height})")
            # Check maximum size for original image
            if width > 4000 or height > 4000:
                self.image_label.setText("Too large\nMax 4000x4000")
                return
            print("Initial validation checks passed.")
            # Store the original validated pixmap
            self.current_pixmap = original_pixmap
            # Scale the original pixmap for UI preview to fit max dimensions
            preview_display_pixmap = original_pixmap.scaled(
                MAX_PREVIEW_WIDTH,
                MAX_PREVIEW_HEIGHT,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation,
            )
            # Set the image_label to the exact size of the scaled pixmap
            self.image_label.setFixedSize(preview_display_pixmap.size())
            self.image_label.setPixmap(preview_display_pixmap)
            self.image_label.setText("")  # Clear any previous text
            # Emit signals with the original validated pixmap and the file path
            print("Emitting coverArtChanged signals with original pixmap and file path...")
            self.coverArtChanged.emit(original_pixmap)
            self.cover_art_path_changed.emit(file_path)
            print("Cover art loading and UI preview updated.")
        except Exception as e:
            print(f"Error loading cover art: {e}")
            import traceback
            traceback.print_exc()
            self.image_label.setText("Error loading image")
    def get_cover_art(self) -> Optional[QPixmap]:
        """Return the current cover art pixmap."""
        return self.current_pixmap
    def clear_cover_art(self) -> None:
        """Clear the current cover art."""
        self.image_label.setPixmap(QPixmap())  # Clear the image
        self.image_label.setText("Drag Image Here") # Restore text
        # Reset label to default size when cleared
        self.image_label.setFixedSize(DEFAULT_PREVIEW_WIDTH, DEFAULT_PREVIEW_HEIGHT)
        self.current_pixmap = None
    def set_cover_art_from_pixmap(self, pixmap: QPixmap) -> None:
        """Set the cover art from a QPixmap."""
        if pixmap and not pixmap.isNull():
            original_pixmap = pixmap  # Use this name for clarity
            # Check original dimensions before any transformation
            width = original_pixmap.width()
            height = original_pixmap.height()
            # Log aspect ratio information for original image
            if width != height:
                print(f"Warning: Image is not 1:1 aspect ratio ({width}x{height})")
            # Check maximum size for original image
            if width > 4000 or height > 4000:
                self.current_pixmap = None
                self.image_label.setPixmap(QPixmap())  # Clear image_label display
                self.image_label.setText("Too large\nMax 4000x4000")  # Set error message
                self.image_label.setFixedSize(DEFAULT_PREVIEW_WIDTH, DEFAULT_PREVIEW_HEIGHT)  # Reset to default size
                self.coverArtChanged.emit(QPixmap())  # Emit empty on failure
                return
            print("Initial validation checks passed for pixmap.")
            # Store the original validated pixmap
            self.current_pixmap = original_pixmap
            # Scale the original pixmap for UI preview to fit max dimensions
            preview_display_pixmap = original_pixmap.scaled(
                MAX_PREVIEW_WIDTH,
                MAX_PREVIEW_HEIGHT,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation,
            )
            # Set the image_label to the exact size of the scaled pixmap
            self.image_label.setFixedSize(preview_display_pixmap.size())
            self.image_label.setPixmap(preview_display_pixmap)
            self.image_label.setText("")  # Clear text
            # Emit signal with the original validated pixmap
            self.coverArtChanged.emit(original_pixmap)
        else:
            # Handle null or invalid input pixmap
            self.clear_cover_art()  # Clears current_pixmap and UI
            self.coverArtChanged.emit(QPixmap())
    def dragEnterEvent(self, event: QDragEnterEvent) -> None:
        """Handle drag enter events for image files."""
        if event.mimeData().hasUrls():
            url = event.mimeData().urls()[0]
            file_path = Path(url.toLocalFile())
            if file_path.suffix.lower() in self.SUPPORTED_EXTENSIONS:
                event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()
    def dragMoveEvent(self, event: QDragEnterEvent) -> None:
        """Handle drag move events."""
        if event.mimeData().hasUrls():
            url = event.mimeData().urls()[0]
            file_path = Path(url.toLocalFile())
            if file_path.suffix.lower() in self.SUPPORTED_EXTENSIONS:
                event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()
    def dropEvent(self, event: QDropEvent) -> None:
        """Handle drop events for image files."""
        if event.mimeData().hasUrls():
            url = event.mimeData().urls()[0]
            file_path = url.toLocalFile()
            self.set_cover_art(file_path)
            event.acceptProposedAction()
        else:
            event.ignore()
    def mousePressEvent(self, event: QMouseEvent) -> None:
        """Handle mouse press events to open file dialog."""
        if event.button() == Qt.MouseButton.LeftButton:
            self.load_cover_art()
        super().mousePressEvent(event) # Call base class implementation
    def update_cover_art_display(self, image_data: Optional[bytes]):
        """Update the cover art display with image data.
        This slot is connected to MainController.cover_art_updated_signal and
        displays the cover art from the provided image data bytes.
        Args:
            image_data: Cover art image data as bytes, QPixmap, or None
        """
        if image_data is not None:
            # Check if it's already a QPixmap
            if isinstance(image_data, QPixmap):
                # Directly use the pixmap
                self.set_cover_art_from_pixmap(image_data)
            else:
                # Assume it's bytes data
                # 1. Memory Bounds Checking
                MAX_IMAGE_DATA_SIZE = 50 * 1024 * 1024  # 50 MB
                if len(image_data) > MAX_IMAGE_DATA_SIZE:
                    self.clear_cover_art()
                    self.image_label.setText("Image too large (max 50MB)")
                    return
                # Create a QPixmap and load data
                pixmap = QPixmap()
                if pixmap.loadFromData(image_data):
                    # Use the existing method to handle display and validation
                    self.set_cover_art_from_pixmap(pixmap)
                else:
                    # 2. Consistent Error State Management: Failed to load image data
                    self.clear_cover_art() # Ensure clear_cover_art is called
                    self.image_label.setText("Invalid image data")
        else:
            # No image data, clear the display
            self.clear_cover_art()
```

## File: src/abb/controllers/main_controller.py
```python
"""MainController for Audiobook Boss.
This controller orchestrates the services and provides a clean interface
for the UI components to interact with.
"""
import logging
import os
from pathlib import Path  # Added for Path object usage
from typing import Any, Dict, List, Optional
from PySide6.QtCore import QObject, Signal
from ..ffmpeg_utils import get_audio_properties
from ..services.file_service import FileService
from ..services.metadata_service import MetadataService
from ..services.path_service import PathService
from ..services.processing_service import ProcessingService
from ..services.settings_manager import SettingsManager
class MainController(QObject):
    """Controller that orchestrates the services and provides a clean interface
    for the UI components to interact with.
    This controller follows the MVC pattern, where it acts as the intermediary
    between the Model (services) and the View (UI components).
    """
    file_list_updated_signal = Signal(list)
    metadata_updated_signal = Signal(dict)
    cover_art_updated_signal = Signal(object)
    status_message_updated_signal = Signal(str)
    processing_started_signal = Signal()
    processing_progress_signal = Signal(int)
    processing_finished_signal = Signal(str)
    error_occurred_signal = Signal(str)
    setting_changed = Signal(str, object)
    selected_file_data_changed = Signal(dict) # Signal that emits a dictionary
    selected_file_properties_updated_signal = Signal(dict)  # New signal for file properties
    combined_size_updated_signal = Signal(str)  # New signal for combined size
    def __init__(self, settings_manager: SettingsManager):
        """Initialize the MainController with all required services."""
        super().__init__()
        self._logger = logging.getLogger("AudiobookBoss.MainController")
        self._file_service = FileService()
        self._metadata_service = MetadataService()
        self._processing_service = ProcessingService()
        self._settings_manager = settings_manager
        self._path_service = PathService()
        self._connect_service_signals()
        self._ensure_default_output_settings()
    def _connect_service_signals(self):
        """Connect to signals emitted by services."""
        self._file_service.files_changed.connect(self._on_files_changed)
        self._file_service.combined_size_changed_signal.connect(self.combined_size_updated_signal)
        # Phase 1B: Connect to consolidated metadata signals
        self._metadata_service.metadata_loaded.connect(self._on_metadata_loaded)
        self._metadata_service.metadata_updated.connect(self._on_metadata_updated)
        self._metadata_service.metadata_error.connect(self._on_metadata_error)
        self._processing_service.progress.connect(self.processing_progress_signal)
        self._processing_service.finished.connect(self.processing_finished_signal)
        self._processing_service.error.connect(self.error_occurred_signal)
        self._processing_service.status.connect(self.status_message_updated_signal)
        self._settings_manager.settings_changed.connect(self._on_setting_changed)
    def _ensure_default_output_settings(self):
        """Ensure output settings have proper defaults if not already set."""
        defaults = {
            "output_directory": str(Path.home() / "AudiobookBoss_Output"),
            "output_filename_pattern": 0,
            "output_bitrate": 64,
            "output_sample_rate": "auto",  # Default to auto pass-through
            "output_channels": 1,  # 1 = mono
            "output_create_subdirectory": True,
        }
        for key, default_value in defaults.items():
            if self._settings_manager.get_setting(key) is None:
                self._settings_manager.set_setting(key, default_value)
    def disconnect_service_signals(self):
        """Disconnects signals that were connected in _connect_service_signals().
        This is important for proper cleanup and to prevent signal-slot
        connections from persisting longer than needed, which can lead to
        unexpected behavior or memory leaks.
        """
        # Disconnect FileService signals
        if hasattr(self, '_file_service') and self._file_service:
            try:
                self._file_service.files_changed.disconnect(self._on_files_changed)
            except TypeError: # Signal not connected or already disconnected
                pass
            try:
                self._file_service.combined_size_changed_signal.disconnect(self.combined_size_updated_signal)
            except TypeError:
                pass
        # Disconnect MetadataService signals
        if hasattr(self, '_metadata_service') and self._metadata_service:
            try:
                self._metadata_service.metadata_loaded.disconnect(self._on_metadata_loaded)
            except TypeError:
                pass
            try:
                self._metadata_service.metadata_updated.disconnect(self._on_metadata_updated)
            except TypeError:
                pass
            try:
                self._metadata_service.metadata_error.disconnect(self._on_metadata_error)
            except TypeError:
                pass
        # Disconnect ProcessingService signals
        if hasattr(self, '_processing_service') and self._processing_service:
            try:
                self._processing_service.progress.disconnect(self.processing_progress_signal)
            except TypeError:
                pass
            try:
                self._processing_service.finished.disconnect(self.processing_finished_signal)
            except TypeError:
                pass
            try:
                self._processing_service.error.disconnect(self.error_occurred_signal)
            except TypeError:
                pass
            try:
                self._processing_service.status.disconnect(self.status_message_updated_signal)
            except TypeError:
                pass
        # Disconnect SettingsManager signals
        if hasattr(self, '_settings_manager') and self._settings_manager:
            try:
                self._settings_manager.settings_changed.disconnect(self._on_setting_changed)
            except TypeError:
                pass
    def _on_files_changed(self, files: List[str]):
        """Handle files_changed signal from FileService.
        Args:
            files: Updated list of files
        """
        self.file_list_updated_signal.emit(files)
        # Load metadata from first file if files were added and no metadata exists
        if files and self._metadata_service.current_metadata is None:
            first_file_path = files[0]
            self._metadata_service.extract_and_load_metadata(first_file_path)
    def _on_metadata_loaded(self, metadata: Dict[str, Any]):
        """Handle metadata_loaded signal from MetadataService.
        Args:
            metadata: Loaded metadata dictionary (may include cover_art_data)
        """
        self.metadata_updated_signal.emit(metadata)
        # Handle cover art data if present
        if "cover_art_data" in metadata:
            self.cover_art_updated_signal.emit(metadata.get("cover_art_data"))
    def _on_metadata_updated(self, metadata: Dict[str, Any]):
        """Handle metadata_updated signal from MetadataService.
        Args:
            metadata: Updated metadata dictionary (may include cover_art_path)
        """
        self.metadata_updated_signal.emit(metadata)
        # Handle cover art path if present
        if "cover_art_path" in metadata:
            self.cover_art_updated_signal.emit(metadata.get("cover_art_path"))
        # Handle cover art data if present
        elif "cover_art_data" in metadata:
            self.cover_art_updated_signal.emit(metadata.get("cover_art_data"))
    def _on_metadata_error(self, error_message: str):
        """Handle metadata_error signal from MetadataService.
        Args:
            error_message: Error message from metadata operations
        """
        self.error_occurred_signal.emit(f"Metadata error: {error_message}")
    def _on_setting_changed(self, key: str, value: Any):
        """Handle settings_changed signal from SettingsManager.
        Args:
            key: Setting key that changed
            value: New value for the setting
        """
        self.setting_changed.emit(key, value)
    # File management methods
    def add_files(self, paths: List[str]) -> List[str]:
        """Add files to the file list.
        Args:
            paths: List of file paths to add
        Returns:
            List of file paths that were actually added
        """
        if paths:
            self._settings_manager.set_setting("last_input_dir", os.path.dirname(paths[0]))
            # Settings manager auto-saves on update
        return self._file_service.add_files(paths)
    def remove_file(self, index: int) -> None:
        """Remove a file from the list by index.
        Args:
            index: Index of the file to remove
        """
        self._file_service.remove_file(index)
    def reorder_files(self, new_order_paths: list) -> None:
        """Reorder files according to the provided list of file paths.
        Args:
            new_order_paths: List of file paths representing the new order
        """
        self._file_service.reorder_files(new_order_paths)
    def get_files(self) -> List[str]:
        """Get the current list of files.
        Returns:
            List of file paths
        """
        return self._file_service.get_files()
    def clear_file_list(self) -> None:
        """Clear all files from the list."""
        current_files = self._file_service.get_files()
        for _ in range(len(current_files)):
            self._file_service.remove_file(0)
    def extract_metadata(self, file_path: str) -> Dict[str, Any]:
        """Extract metadata from an audio file.
        Args:
            file_path: Path to the audio file
        Returns:
            Dictionary containing metadata fields
        """
        return self._metadata_service.extract_metadata(file_path)
    def update_metadata(self, field_key: str, new_value: Any) -> None:
        """Update a metadata field.
        Args:
            field_key: Key of the field that changed (raw from UI)
            new_value: New value for the field
        """
        field_key_mapping = {
            "title": "title", "author": "artist", "narrator": "narrator",
            "series": "series", "series_index": "series_position", "year": "year",
            "genre": "genre", "description": "description"
        }
        mapped_key = field_key_mapping.get(field_key, field_key)
        self.update_metadata_field(mapped_key, new_value)
    def get_metadata(self) -> Dict[str, Any]:
        """Get a copy of the current metadata.
        Returns:
            Dictionary containing metadata fields
        """
        return self._metadata_service.get_metadata()
    def update_metadata_field(self, field_name: str, value: str):
        """Update a specific metadata field in the MetadataService.
        Args:
            field_name: The name of the metadata field to update.
            value: The new value for the field.
        """
        self._metadata_service.update_current_metadata(field_name, value)
    def update_cover_art(self, image_path: str):
        """Update the cover art path in the MetadataService.
        Args:
            image_path: The path to the new cover art image.
        """
        self._metadata_service.set_cover_art(image_path)
    def start_preview(self, input_file_path: str, metadata: Dict[str, Any], 
                     settings: Dict[str, Any], temp_cover_path: Optional[str] = None, 
                     duration_seconds: int = 30) -> None:
        """Start processing a preview of an audio file.
        Args:
            input_file_path: Path to the input audio file
            metadata: Metadata to apply to the preview
            settings: Processing settings
            temp_cover_path: Path to temporary cover art file (optional)
            duration_seconds: Duration of the preview in seconds
        """
        self._processing_service.process_preview(
            input_file_path, metadata, settings, temp_cover_path, duration_seconds
        )
    def start_process(self, input_files: List[str], output_path: str, 
                     output_filename: str, metadata: Dict[str, Any], 
                     settings: Dict[str, Any]) -> None:
        """Start processing audio files into a single output file.
        Args:
            input_files: List of input audio file paths
            output_path: Directory to save the output file
            output_filename: Name of the output file
            metadata: Metadata to apply to the output file
            settings: Processing settings
        """
        self.processing_started_signal.emit()
        self.status_message_updated_signal.emit("Starting processing...")
        self._processing_service.process_full(
            input_files, output_path, output_filename, metadata, settings
        )
    def cancel_processing(self) -> None:
        """Cancel the current processing operation."""
        self._processing_service.cancel()
    def calculate_output_path(self) -> str:
        """Calculate output path using path service based on settings and metadata."""
        home_dir = str(Path.home())
        base_dir = self._settings_manager.get_setting("output_directory", home_dir)
        metadata = self._metadata_service.get_metadata()
        use_subdirectory = self._settings_manager.get_setting("output_create_subdirectory", True)
        return self._path_service.calculate_output_path(base_dir, metadata, use_subdirectory)
    def generate_output_filename(self) -> str:
        """Generate output filename using path service based on pattern and metadata."""
        metadata = self._metadata_service.get_metadata()
        pattern_id = self._settings_manager.get_setting("output_filename_pattern", 0)
        return self._path_service.generate_output_filename(metadata, pattern_id)
    def get_output_path(self) -> str:
        """Calculate the output path based on settings and metadata."""
        # This method now calls the already moved internal method.
        return self.calculate_output_path()
    def get_output_filename(self) -> str:
        """Generate output filename based on selected pattern and metadata."""
        # This method now calls the already moved internal method.
        return self.generate_output_filename()
    def configure_audio_defaults(self, file_path: str) -> None:
        """Set default audio settings based on first file properties."""
        # The method 'set_default_bitrate_from_file' was originally in MainWindow
        # and has not been moved to MainController yet.
        # This is a placeholder implementation as per the subtask description.
        if hasattr(self, 'set_default_bitrate_from_file'):
            # This block will not be executed until set_default_bitrate_from_file is part of MainController
            self.set_default_bitrate_from_file(file_path)
        else:
            # TODO: Implement or move set_default_bitrate_from_file to MainController
            # For now, logging a warning as per code review suggestion.
            self._logger.warning(f"Method 'set_default_bitrate_from_file' not found in MainController. Called with: {file_path}")
            # Depending on requirements, this could be a log message or raise NotImplementedError
            pass
    # Settings methods
    def get_setting(self, key: str, default: Any = None) -> Any:
        """Get a setting value by key.
        Args:
            key: The setting key to retrieve
            default: Value to return if key doesn't exist
        Returns:
            The setting value or default if key doesn't exist
        """
        return self._settings_manager.get_setting(key, default)
    def update_setting(self, key: str, value: Any) -> None:
        """Update a setting value.
        Args:
            key: The setting key to update
            value: The new value for the setting
        """
        self._settings_manager.set_setting(key, value)
    def update_output_setting(self, setting_name: str, value: Any) -> None:
        """Update a specific output setting.
        This method is intended to be called by the UI when an output setting changes.
        It delegates the update directly to the SettingsManager.
        Args:
            setting_name: The name of the output setting to update.
            value: The new value for the setting.
        """
        self._settings_manager.set_setting(setting_name, value)
    def get_all_output_settings(self) -> Dict[str, Any]:
        """Retrieve all relevant output settings from the SettingsManager.
        This method provides a consolidated dictionary of output settings,
        using default values if a setting is not explicitly found in the
        SettingsManager.
        Returns:
            A dictionary containing all current output settings.
        """
        return {
            "output_directory": self._settings_manager.get_setting("output_directory", str(Path.home() / "AudiobookBoss_Output")),
            "output_filename_pattern": self._settings_manager.get_setting("output_filename_pattern", 0),
            "output_bitrate": self._settings_manager.get_setting("output_bitrate", 64),
            "output_sample_rate": self._settings_manager.get_setting("output_sample_rate", "auto"),  # Default to auto pass-through
            "output_channels": self._settings_manager.get_setting("output_channels", 1),  # 1 = mono
            "output_create_subdirectory": self._settings_manager.get_setting("output_create_subdirectory", True),
        }
    def show_settings_dialog(self) -> None:
        """Show the settings dialog.
        This method creates and shows a SettingsDialog instance using the 
        current settings and processes the dialog results.
        """
        from ..ui.dialogs.settings_dialog import SettingsDialog
        settings = {key: self._settings_manager.get_setting(key) for key in 
                  ["last_input_dir", "last_output_dir", "default_bitrate", 
                   "default_channels", "use_subdirectory_pattern", "filename_pattern"]}
        settings_dialog = SettingsDialog(settings)
        if settings_dialog.exec_():
            updated_settings = settings_dialog.get_updated_settings()
            for key, value in updated_settings.items():
                self.update_setting(key, value)
            self.status_message_updated_signal.emit("Settings updated")
    def show_about_dialog(self) -> None:
        """Show the about dialog.
        This method creates and shows an AboutDialog instance.
        """
        from ..ui.dialogs.about_dialog import AboutDialog
        about_dialog = AboutDialog()
        about_dialog.exec_()
    def handle_file_selection_changed(self, selected_files: List[str]) -> None:
        """Handle file selection changes from the UI.
        Args:
            selected_files: List of selected file paths
        """
        if selected_files and len(selected_files) == 1:
            filepath = selected_files[0]
            # Extract properties using ffmpeg_utils
            properties, error = get_audio_properties(filepath)
            if error:
                self.selected_file_properties_updated_signal.emit({})
            else:
                self.selected_file_properties_updated_signal.emit(properties)
            # Optionally, also extract metadata as before
            self._metadata_service.extract_metadata(filepath)
            current_metadata = self._metadata_service.get_metadata()
            self.selected_file_data_changed.emit(current_metadata if current_metadata else {})
        else:
            self.selected_file_properties_updated_signal.emit({})
            self.selected_file_data_changed.emit({})
    def start_processing(self) -> None:
        """Start processing audiobook files.
        This method gathers all necessary data from services and initiates
        the audio processing. It implements Feature 1.7.4 from the plan.
        """
        # Get file list from FileService
        file_list = self._file_service.get_file_list()
        if not file_list:
            self.error_occurred_signal.emit("No files to process")
            self.status_message_updated_signal.emit("Error: No files selected for processing")
            return
        # Get metadata from MetadataService
        metadata = self._metadata_service.get_metadata()
        if not metadata:
            metadata = {}
        # Get cover art path
        cover_art_path = self._metadata_service.get_cover_art_path()
        # Get output settings from SettingsManager
        output_settings = {
            'output_directory': self._settings_manager.get_setting('output_directory'),
            'output_filename_pattern': self._settings_manager.get_setting('output_filename_pattern', 0),
            'use_subdirectory_pattern': self._settings_manager.get_setting('output_create_subdirectory', True),
            'output_bitrate': self._settings_manager.get_setting('output_bitrate', 64),
            'output_channels': self._settings_manager.get_setting('output_channels', 1),
        }
        # Handle sample rate - only include if not "auto"
        sample_rate = self._settings_manager.get_setting('output_sample_rate', 'auto')
        if sample_rate != 'auto' and sample_rate:
            output_settings['output_sample_rate'] = sample_rate
        # Emit processing started signal
        self.processing_started_signal.emit()
        self.status_message_updated_signal.emit("Starting audio processing...")
        # Start processing
        try:
            self._processing_service.start_processing(
                file_list=file_list,
                output_settings=output_settings,
                metadata=metadata,
                cover_art_path=cover_art_path
            )
        except Exception as e:
            self.error_occurred_signal.emit(f"Failed to start processing: {str(e)}")
            self.status_message_updated_signal.emit(f"Error: {str(e)}")
```

## File: src/abb/main_window.py
```python
"""MainWindow class for Audiobook Boss (ABB).
Inherits QMainWindow, sets default size to 1280x820 (see Style Guide §2).
"""
import logging
import os
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional
from PySide6.QtCore import (
    QEvent,
    QSettings,
    QSize,
    Qt,
)
from PySide6.QtGui import QAction, QPalette
from PySide6.QtWidgets import (
    QFileDialog,
    QLabel,
    QMainWindow,
    QMessageBox,
    QSplitter,
    QStatusBar,
    QVBoxLayout,
    QWidget,
)
from .controllers.main_controller import MainController
from .ffmpeg_utils import (
    _get_executable_path,
)
from .ffmpeg_utils import (
    calculate_file_size as calculate_file_size_util,
)
from .metadata_utils import (
    calculate_estimated_size,
    get_duration,
)
from .services.settings_manager import SettingsManager
from .ui.widgets.left_panel_widget import LeftPanelWidget
from .ui.widgets.right_panel_widget import RightPanelWidget
def load_qss_file(file_path: Path) -> str:
    """Load and return the content of a QSS file."""
    if file_path.exists():
        with open(file_path, "r") as file:
            return file.read()
    return ""
class MainWindow(QMainWindow):
    """Main application window."""
    def __init__(self) -> None:
        super().__init__()
        self.setWindowTitle("Audiobook Boss")
        self.resize(QSize(1280, 820))
        self.settings = QSettings("AudiobookBoss", "ABB")
        self.logger = logging.getLogger("AudiobookBoss.MainWindow")
        self.app_state = {
            "file_list": [],
            "metadata": {},
            "settings": self.load_settings(),
            "processing_state": {},
        }
        # Create a settings manager for persistent settings
        settings_path = os.path.join(os.path.expanduser("~"), ".audiobookboss", "settings.json")
        self._settings_manager = SettingsManager(settings_path, self.app_state["settings"])
        # Initialize controller with settings manager
        self._controller = MainController(settings_manager=self._settings_manager)
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(12, 12, 12, 10)
        self.main_layout.setSpacing(8)
        self.splitter = QSplitter(Qt.Horizontal)
        self.left_panel = QWidget()
        self.left_panel.setObjectName("left_panel")
        self.right_panel = QWidget()
        self.right_panel.setObjectName("right_panel")
        self._setup_left_panel()
        self._setup_right_panel()
        self._setup_property_labels()
        self.setup_controller_connections()
        self.splitter.addWidget(self.left_panel)
        self.splitter.addWidget(self.right_panel)
        self.splitter.setSizes([33, 67]) # 33/67 ratio
        self.main_layout.addWidget(self.splitter)
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self._setup_menu_bar()
        self.apply_theme()
        self._check_system_dependencies()
    def _check_system_dependencies(self) -> None:
        """Check for essential system dependencies like ffmpeg and ffprobe."""
        ffmpeg_path = _get_executable_path("ffmpeg")
        ffprobe_path = _get_executable_path("ffprobe")
        ffmpeg_ok = ffmpeg_path is not None
        ffprobe_ok = ffprobe_path is not None
        if not ffmpeg_ok or not ffprobe_ok:
            self.show_ffmpeg_ffprobe_warning_dialog(ffmpeg_ok, ffprobe_ok)
    def show_ffmpeg_ffprobe_warning_dialog(
        self, ffmpeg_ok: bool, ffprobe_ok: bool
    ) -> None:
        """Display a warning dialog if ffmpeg or ffprobe are not found."""
        missing_tools = []
        if not ffmpeg_ok:
            missing_tools.append("FFmpeg")
        if not ffprobe_ok:
            missing_tools.append("FFprobe")
        tool_list_str = " and ".join(missing_tools)
        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Warning)
        msg_box.setWindowTitle(f"{tool_list_str} Not Found")
        msg_box.setText(
            f"The essential command-line tool(s) {tool_list_str} could not be found. "
            f"Audiobook Boss needs these tools to process audio files.\n\n"
            f"Please ensure that {tool_list_str} is installed and accessible in your system's PATH, "
            f"or that it is included in the application's bundled resources.\n\n"
            f"You may need to configure the paths in the application settings if they are installed in a custom location."
        )
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec()
    def event(self, event: QEvent) -> bool:
        """Handle application events."""
        if event.type() == QEvent.ApplicationPaletteChange:
            self.apply_theme()
        return super().event(event)
    def apply_theme(self) -> None:
        """Apply appropriate theme based on system palette."""
        app_palette = self.palette()
        is_dark_mode = app_palette.color(QPalette.Window).lightness() < 128
        self.load_stylesheet(is_dark_mode)
    def load_stylesheet(self, is_dark_mode: bool) -> None:
        """Load and apply the appropriate stylesheet."""
        if getattr(sys, "frozen", False):
            base_path = Path(sys._MEIPASS) # Running as compiled app
        else:
            base_path = Path(__file__).parent # Running in development
        stylesheet_name = "style_dark.qss" if is_dark_mode else "style_light.qss"
        stylesheet_path = base_path / "Resources" / stylesheet_name
        stylesheet_content = load_qss_file(stylesheet_path)
        if stylesheet_content:
            self.setStyleSheet(stylesheet_content)
        else:
            # Minimal default styling if stylesheet doesn't exist
            self.setStyleSheet("QSplitter::handle { background-color: palette(mid); }")
    def load_settings(self) -> dict:
        """Load settings from QSettings."""
        settings_dict = {
            "last_input_dir": self.settings.value("last_input_dir", str(Path.home()), type=str),
            "last_output_dir": self.settings.value("last_output_dir", str(Path.home()), type=str),
            "output_bitrate": self.settings.value("output_bitrate", 64, type=int),
            "output_channels": self.settings.value("output_channels", 1, type=int), # 1 = mono
            "output_sample_rate": self.settings.value("output_sample_rate", 44100, type=int),  # Auto-detected from input
            "output_directory": self.settings.value("output_directory", str(Path.home()), type=str),
            "output_filename_pattern": self.settings.value("output_filename_pattern", 0, type=int),
            "output_create_subdirectory": self.settings.value("output_create_subdirectory", False, type=bool),
            "use_subdirectory_pattern": self.settings.value("use_subdirectory_pattern", True, type=bool),
            "filename_pattern": self.settings.value("filename_pattern", 0, type=int), # 0 = default pattern
        }
        return settings_dict
    def save_settings(self) -> None:
        """Save current settings to QSettings."""
        self.settings.setValue("last_input_dir", self.app_state["settings"]["last_input_dir"])
        self.settings.setValue("last_output_dir", self.app_state["settings"]["last_output_dir"])
        self.settings.setValue("output_bitrate", self.app_state["settings"]["output_bitrate"])
        self.settings.setValue("output_channels", self.app_state["settings"]["output_channels"])
        self.settings.setValue("output_sample_rate", self.app_state["settings"]["output_sample_rate"])
        self.settings.setValue("output_directory", self.app_state["settings"]["output_directory"])
        self.settings.setValue("output_filename_pattern", self.app_state["settings"]["output_filename_pattern"])
        self.settings.setValue("output_create_subdirectory", self.app_state["settings"]["output_create_subdirectory"])
        self.settings.setValue("use_subdirectory_pattern", self.app_state["settings"]["use_subdirectory_pattern"])
        self.settings.setValue("filename_pattern", self.app_state["settings"]["filename_pattern"])
        self.settings.sync()
    def update_setting(self, key: str, value) -> None:
        """Update a specific setting and save it."""
        self._controller.update_setting(key, value)
    @property
    def metadata_form_widget(self):
        """Convenience property to access the metadata form widget."""
        return self.right_panel_widget.metadata_form_widget
    def _on_setting_changed(self, key: str, value) -> None:
        """Handle setting changes from the UI widgets.
        Args:
            key: Setting key that changed
            value: New value for the setting
        """
        # Update local app_state first
        self.app_state["settings"][key] = value
        # Then update through the controller which will save to SettingsManager
        self._controller.update_setting(key, value)
    def closeEvent(self, event) -> None:
        """Save settings and clean up when the application is closing."""
        self.save_settings()
        # Removed processing_thread and processing_worker cleanup; handled by controller now
        super().closeEvent(event)
    def _setup_left_panel(self) -> None:
        """Set up the left panel with input components using LeftPanelWidget."""
        left_layout = QVBoxLayout(self.left_panel)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(0)
        self.left_panel_widget = LeftPanelWidget()
        self.left_panel_widget.files_dropped_signal.connect(lambda files: self._controller.add_files(files))
        self.left_panel_widget.selection_changed_signal.connect(self._on_file_selection_changed)
        self.left_panel_widget.request_remove_signal.connect(self.remove_file)
        self.left_panel_widget.request_move_up_signal.connect(self._on_move_file_up_requested)
        self.left_panel_widget.request_move_down_signal.connect(self._on_move_file_down_requested)
        self.left_panel_widget.request_add_files_signal.connect(self._on_add_files_requested)
        self.left_panel_widget.request_clear_list_signal.connect(self.clear_file_list)
        self.left_panel_widget.files_reordered_signal.connect(self._handle_files_reordered_in_ui)
        left_layout.addWidget(self.left_panel_widget)
    def _on_file_selection_changed(self, index: int) -> None:
        """Handle file selection changed in the LeftPanelWidget.
        Delegates to the controller.
        Args:
            index: Index of the selected file
        """
        if index >= 0 and index < len(self.app_state["file_list"]):
            file_path = self.app_state["file_list"][index]
            self._controller.handle_file_selection_changed([file_path])
        else:
            self._controller.handle_file_selection_changed([])
    def _update_ui_for_selected_file(self, properties: Optional[Dict[str, Any]] = None, metadata: Optional[Dict[str, Any]] = None) -> None:
        """Update UI elements based on the currently selected file's properties and metadata.
        This method is intended to be called by a signal from the controller.
        """
        is_first_file = False
        if self.app_state["file_list"] and self.left_panel_widget.file_list_widget.currentRow() == 0:
            is_first_file = True
        self._set_file_property_labels(properties, is_first_file)
        if properties:
            file_properties = {
                "duration": str(properties.get("duration", "N/A")),
                "bitrate": f"{properties.get('bitrate', 'N/A')} kbps",
                "sample_rate": f"{properties.get('sample_rate', 'N/A')} Hz",
                "channels": str(properties.get("channels", "N/A"))
            }
            self.left_panel_widget.update_selected_file_properties_display(file_properties)
        else:
            self.left_panel_widget.update_selected_file_properties_display({}) # Clear properties if no file or error
        if metadata:
            self.right_panel_widget.populate_metadata(metadata)
        else:
            self.right_panel_widget.populate_metadata({}) # Clear metadata if no file or error
    def _on_move_file_up_requested(self, index: int) -> None:
        """Handle move file up request from LeftPanelWidget.
        Args:
            index: Index of the file to move up
        """
        if index > 0 and index < len(self.app_state["file_list"]):
            file_list = self.app_state["file_list"].copy()
            file_list[index], file_list[index-1] = file_list[index-1], file_list[index]
            self._update_file_list_display(file_list)
            self.left_panel_widget.file_list_widget.setCurrentRow(index-1)
    def _on_move_file_down_requested(self, index: int) -> None:
        """Handle move file down request from LeftPanelWidget.
        Args:
            index: Index of the file to move down
        """
        if index >= 0 and index < len(self.app_state["file_list"]) - 1:
            file_list = self.app_state["file_list"].copy()
            file_list[index], file_list[index+1] = file_list[index+1], file_list[index]
            self._update_file_list_display(file_list)
            self.left_panel_widget.file_list_widget.setCurrentRow(index+1)
    def _handle_files_reordered_in_ui(self, new_order_paths: List[str]) -> None:
        """Handle files reordered signal from LeftPanelWidget.
        Calls the controller's reorder_files method with the new order.
        Args:
            new_order_paths: List of file paths in the new order
        """
        self._controller.reorder_files(new_order_paths)
    def _setup_menu_bar(self) -> None:
        """Set up the menu bar with Settings and About actions."""
        menu_bar = self.menuBar()
        file_menu = menu_bar.addMenu("&File")
        settings_action = QAction("&Settings...", self)
        settings_action.setStatusTip("Open application settings")
        settings_action.triggered.connect(self._show_settings_dialog)
        file_menu.addAction(settings_action)
        help_menu = menu_bar.addMenu("&Help")
        about_action = QAction("&About Audiobook Boss", self)
        about_action.setStatusTip("Show information about this application")
        about_action.triggered.connect(self._show_about_dialog)
        help_menu.addAction(about_action)
    def _setup_property_labels(self):
        """Create property labels that are potentially still needed for the right panel."""
        # These labels might be redundant if RightPanelWidget and its children handle all displays.
        # MainWindow._setup_output_audio_settings_group also creates self.sample_rate_output_label.
        self.sample_rate_output_label = QLabel("—") 
        self.combined_size_label = QLabel("—")
        self.file_size_label = QLabel("—")
        for label in [self.sample_rate_output_label, self.combined_size_label, self.file_size_label]:
            label.setStyleSheet("font-family: 'Menlo', 'Monaco', 'Consolas', 'DejaVu Sans Mono', monospace;")
    def remove_file(self, index: int) -> None:
        """Remove a file from the list by index.
        Delegates to the controller.
        Args:
            index: Index of the file to remove
        """
        self._controller.remove_file(index)
    def _on_add_files_requested(self) -> None:
        """Handle add files button click from LeftPanelWidget.
        Opens a file dialog and adds selected files to the file list.
        """
        open_dir = self.app_state["settings"]["last_input_dir"]
        file_dialog = QFileDialog(self)
        file_dialog.setFileMode(QFileDialog.ExistingFiles)
        file_dialog.setNameFilter("Audio Files (*.mp3 *.m4a *.m4b *.aac *.mp4 *.wav *.flac)")
        file_dialog.setDirectory(open_dir)
        if file_dialog.exec_():
            file_paths = file_dialog.selectedFiles()
            if file_paths:
                self._controller.add_files(file_paths)
    def clear_file_list(self) -> None:
        """Clear all files from the list.
        Delegates to the controller to handle the operation.
        """
        self._controller.clear_file_list()
    def _update_file_list_display(self, files: List[str]) -> None:
        """Update the file list widget with the provided files.
        Args:
            files: List of file paths to display
        """
        self.app_state["file_list"] = files
        self.left_panel_widget.update_file_list_display(files)
        self.update_combined_size()
        self.update_estimated_size()
    def format_file_size(self, size_bytes: int) -> str:
        """Format file size in bytes to a human-readable string (KB, MB, GB)."""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.2f} GB"
    def update_combined_size(self) -> None:
        """Calculate and update the combined size of all files in the list."""
        total_size = 0
        for file_path in self.app_state["file_list"]:
            total_size += calculate_file_size_util(file_path)
        self.combined_size_label.setText(self.format_file_size(total_size))
    def _set_file_property_labels(
        self, properties: Optional[Dict[str, Any]], is_first_file: bool
    ) -> None:
        """Set the file property labels based on the fetched properties."""
        if properties:
            self.file_size_label.setText(self.format_file_size(properties.get("file_size", 0)))
            if is_first_file:
                self.sample_rate_output_label.setText(f"{properties.get('sample_rate', 'N/A')} Hz")
        else: 
            self.file_size_label.setText("N/A")
            if is_first_file:
                self.sample_rate_output_label.setText("N/A")
    def _setup_right_panel(self) -> None:
        """Set up the right panel with metadata and output components using RightPanelWidget."""
        right_layout = QVBoxLayout(self.right_panel)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(0)
        self.right_panel_widget = RightPanelWidget(self._controller)
        self.right_panel_widget.populate_settings(self.app_state["settings"])
        # Connect to RightPanelWidget's setting_changed signal (which forwards from OutputSettingsWidget)
        self.right_panel_widget.setting_changed.connect(self._on_setting_changed)
        self.right_panel_widget.start_processing_requested.connect(self.start_processing)
        self.right_panel_widget.cancel_processing_requested.connect(self.cancel_processing)
        right_layout.addWidget(self.right_panel_widget)
    def _on_cover_art_update_requested(self, image_path: str) -> None:
        """Handle cover art update request from the RightPanelWidget.
        Args:
            image_path: Path to the new cover art image file
        """
        self._controller.update_cover_art(image_path)
        self.update_estimated_size()
    def update_cover_art(self, pixmap) -> None:
        """Update cover art in metadata."""
        if "metadata" not in self.app_state:
            self.app_state["metadata"] = {}
        self.app_state["metadata"]["cover_art"] = pixmap # Storing QPixmap in app_state
        self.update_estimated_size()
    def _on_cover_art_changed(self, image_path) -> None:
        """Handle cover art changed signal from the MetadataFormWidget.
        Args:
            image_path (str): Path to the new cover art image
        """
        self._controller.update_metadata("cover_art", image_path)
        self.logger.info(f"Cover art updated to: {image_path}")
    def update_bitrate_setting(self) -> None:
        """Update bitrate setting in app state."""
        # Assumes self.bitrate_combo is valid (might be an issue if setup methods are dead)
        bitrate = self.bitrate_combo.currentData() 
        self.app_state["settings"]["default_bitrate"] = bitrate
        self.update_setting("default_bitrate", bitrate)
        self.update_estimated_size()
    def update_channels_setting(self) -> None:
        """Update channels setting in app state."""
        # Assumes self.channels_combo is valid
        channels = self.channels_combo.currentData()
        self.app_state["settings"]["default_channels"] = channels
        self.update_setting("default_channels", channels)
    def update_subdir_setting(self, checked: bool) -> None:
        """Update subdirectory pattern setting in app state."""
        self.app_state["settings"]["use_subdirectory_pattern"] = checked
        self.update_setting("use_subdirectory_pattern", checked)
        self.right_panel_widget.set_use_subdirectory(checked)
    def update_filename_pattern(self, button) -> None:
        """Update filename pattern setting in app state."""
        # Assumes self.filename_pattern_group is valid
        pattern_id = self.filename_pattern_group.id(button)
        self.app_state["settings"]["filename_pattern"] = pattern_id
        self.update_setting("filename_pattern", pattern_id)
    def browse_output_directory(self) -> None:
        """Open directory dialog to select output directory."""
        directory = QFileDialog.getExistingDirectory(
            self,
            "Select Output Directory",
            self.app_state["settings"]["last_output_dir"],
        )
        if directory:
            self.app_state["settings"]["last_output_dir"] = directory
            self.update_setting("last_output_dir", directory)
            self.right_panel_widget.set_output_directory(directory)
    def update_estimated_size(self) -> None:
        """Update the estimated output size based on input files and settings."""
        if not self.app_state["file_list"]:
            self.right_panel_widget.set_estimated_size("—")
            return
        total_duration = 0
        for file_path in self.app_state["file_list"]:
            total_duration += get_duration(file_path)
        bitrate = self.app_state["settings"].get("output_bitrate", 64)
        estimated_size = calculate_estimated_size(total_duration, bitrate)
        self.right_panel_widget.set_estimated_size(self.format_file_size(estimated_size))
    def extract_and_populate_metadata(self, file_path: str) -> None:
        """Extract metadata from a file and populate the UI fields.
        Args:
            file_path: Path to the audio file to extract metadata from
        """
        self._controller.extract_metadata(file_path)
    def _update_metadata_display(self, metadata: Dict[str, Any]) -> None:
        """Update the UI fields with the provided metadata.
        Args:
            metadata: Dictionary containing metadata fields
        """
        self.app_state["metadata"] = metadata
        self.right_panel_widget.populate_metadata(metadata)
    # Methods set_default_bitrate_from_file, _determine_optimal_bitrate, 
    # _update_bitrate_ui, and _update_sample_rate_label were removed from MainWindow.
    # Their functionality is intended to be handled by MainController.configure_audio_defaults().
    # Any calls to self.set_default_bitrate_from_file(file_path) should be replaced with
    # self._controller.configure_audio_defaults(file_path).
    def start_processing(self) -> None:
        """Start processing the audiobook.
        Delegates to the controller to handle the processing.
        """
        # Set output file path for verification in _processing_done
        output_path = self._controller.get_output_path()
        output_filename = self._controller.get_output_filename()
        self._output_file_path = str(Path(output_path) / output_filename)
        # Start processing - controller will emit processing_started_signal which triggers UI updates
        self._controller.start_processing()
    def _set_main_ui_enabled(self, enabled: bool) -> None:
        """Toggle enabled state of primary UI input widgets."""
        self.left_panel_widget.setEnabled(enabled)
        # RightPanelWidget's process/cancel buttons are handled by its set_processing_state method
        self.right_panel_widget.set_metadata_form_enabled(enabled)
        self.right_panel_widget.set_output_settings_enabled(enabled)
    def setup_controller_connections(self) -> None:
        """Connect signals from the controller to the UI update methods."""
        self._controller.file_list_updated_signal.connect(self._update_file_list_display)
        self._controller.metadata_updated_signal.connect(self._update_metadata_display)
        self._controller.selected_file_properties_updated_signal.connect(self._on_selected_file_properties_updated)
        self._controller.selected_file_data_changed.connect(self._on_selected_file_metadata_updated)
        self._controller.combined_size_updated_signal.connect(self.combined_size_label.setText)
        # Also update the metadata form widget when metadata changes
        self._controller.metadata_updated_signal.connect(self._on_metadata_updated_from_controller)
        self._controller.cover_art_updated_signal.connect(self._on_cover_art_updated_from_controller)
        self._controller.status_message_updated_signal.connect(self._update_status)
        self._controller.processing_started_signal.connect(lambda: self._set_ui_for_processing_start(True))
        self._controller.processing_progress_signal.connect(self._update_progress)
        self._controller.processing_finished_signal.connect(self._processing_done)
        self._controller.error_occurred_signal.connect(self._processing_error)
        # Connect controller.setting_changed only for specific auto-detected settings
        self._controller.setting_changed.connect(self._on_controller_setting_changed)
    def _on_selected_file_properties_updated(self, properties: Dict[str, Any]) -> None:
        """Handle selected file properties updated signal from controller."""
        self._update_ui_for_selected_file(properties=properties)
    def _on_metadata_updated_from_controller(self, metadata: Dict[str, Any]) -> None:
        """Delegate metadata update to the right panel widget."""
        self.right_panel_widget.metadata_form_widget.update_metadata_display(metadata)
    def _on_cover_art_updated_from_controller(self, cover_art_data) -> None:
        """Delegate cover art update to the right panel widget."""
        self.right_panel_widget.metadata_form_widget.cover_art.update_cover_art_display(cover_art_data)
    def _on_controller_setting_changed(self, key: str, value) -> None:
        """Handle setting changes from the controller (auto-detected values).
        Args:
            key: Setting key that changed
            value: New value for the setting
        """
        # Update local app_state
        self.app_state["settings"][key] = value
        # Update UI for specific auto-detected settings
        if key == "output_sample_rate":
            # Update the output settings widget with the detected sample rate
            self.right_panel_widget.output_settings_widget.set_sample_rate(value)
    def _on_selected_file_metadata_updated(self, metadata: Dict[str, Any]) -> None:
        """Handle selected file metadata updated signal from controller."""
        self._update_ui_for_selected_file(metadata=metadata)
    def _set_ui_for_processing_start(self, is_starting: bool) -> None:
        """Update UI elements based on processing state."""
        if is_starting:
            self._set_main_ui_enabled(False)
            self.right_panel_widget.set_processing_state(True) # Handles its own buttons
            self.status_bar.showMessage("Processing...")
        else:
            self._set_main_ui_enabled(True)
            self.right_panel_widget.set_processing_state(False) # Handles its own buttons
    def cancel_processing(self) -> None:
        """Cancel the current processing operation."""
        self.status_bar.showMessage("Cancelling...")
        self._controller.cancel_processing()
    def _update_progress(self, value: int) -> None:
        """Update progress bar with the given value."""
        self.right_panel_widget.update_progress(value)
        self.status_bar.showMessage(f"Processing: {value}%")
    def _update_status(self, status: str) -> None:
        """Update status bar with the given status message."""
        self.status_bar.showMessage(status)
    def _processing_done(self, output_file_path: str = None) -> None:
        """Handle processing completion."""
        self._set_ui_for_processing_start(False)
        self.right_panel_widget.reset_progress()
        file_path_to_check = output_file_path if output_file_path else getattr(self, "_output_file_path", None)
        if file_path_to_check and os.path.exists(file_path_to_check):
            self.status_bar.showMessage(f"Processing completed successfully. File saved to: {file_path_to_check}", 5000)
        else:
            self.status_bar.showMessage("Processing completed but output file not found. Check permissions and disk space.", 5000)
    def _processing_error(self, error_message: str) -> None:
        """Handle processing error."""
        self._set_ui_for_processing_start(False)
        self.right_panel_widget.reset_progress()
        self.status_bar.showMessage(f"Error: {error_message}", 5000)
    def _show_settings_dialog(self) -> None:
        """Show the settings dialog and update settings if accepted."""
        self._controller.show_settings_dialog()
    def _show_about_dialog(self) -> None:
        """Show the about dialog."""
        self._controller.show_about_dialog()
```
