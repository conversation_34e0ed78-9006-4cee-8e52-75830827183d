# Generated on: 2025-06-06 14:24:06

# SAFETY NET STATUS
==================

## Code Quality Check
```
src/abb/controllers/__init__.py:1:1: D104 Missing docstring in public package
src/abb/controllers/main_controller.py:22:5: D205 1 blank line required between summary line and description
   |
21 |   class MainController(QObject):
22 | /     """Controller that orchestrates the services and provides a clean interface
23 | |     for the UI components to interact with.
24 | |     
25 | |     This controller follows the MVC pattern, where it acts as the intermediary
26 | |     between the Model (services) and the View (UI components).
27 | |     """
   | |_______^ D205
28 |       
29 |       file_list_updated_signal = Signal(list)
   |
   = help: Insert single blank line

src/abb/controllers/main_controller.py:85:9: D205 1 blank line required between summary line and description
   |
84 |       def disconnect_service_signals(self):
85 | /         """Disconnects signals that were connected in _connect_service_signals().
86 | |         This is important for proper cleanup and to prevent signal-slot
87 | |         connections from persisting longer than needed, which can lead to
88 | |         unexpected behavior or memory leaks.
89 | |         """
   | |___________^ D205
90 |           # Disconnect FileService signals
91 |           if hasattr(self, '_file_service') and self._file_service:
   |
   = help: Insert single blank line

src/abb/controllers/main_controller.py:364:101: E501 Line too long (107 > 100)
    |
362 |         # This is a placeholder implementation as per the subtask description.
363 |         if hasattr(self, 'set_default_bitrate_from_file'):
364 |             # This block will not be executed until set_default_bitrate_from_file is part of MainController
    |                                                                                                     ^^^^^^^ E501
365 |             self.set_default_bitrate_from_file(file_path)
366 |         else:
    |

src/abb/controllers/main_controller.py:369:101: E501 Line too long (129 > 100)
    |
367 |             # TODO: Implement or move set_default_bitrate_from_file to MainController
368 |             # For now, logging a warning as per code review suggestion.
369 |             self._logger.warning(f"Method 'set_default_bitrate_from_file' not found in MainController. Called with: {file_path}")
    |                                                                                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E501
370 |             # Depending on requirements, this could be a log message or raise NotImplementedError
371 |             pass
    |

src/abb/controllers/main_controller.py:418:101: E501 Line too long (130 > 100)
    |
416 |         """
417 |         return {
418 |             "output_directory": self._settings_manager.get_setting("output_directory", str(Path.home() / "AudiobookBoss_Output")),
    |                                                                                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E501
419 |             "output_filename_pattern": self._settings_manager.get_setting("output_filename_pattern", 0),
420 |             "output_bitrate": self._settings_manager.get_setting("output_bitrate", 64),
    |

src/abb/controllers/main_controller.py:419:101: E501 Line too long (104 > 100)
    |
417 |         return {
418 |             "output_directory": self._settings_manager.get_setting("output_directory", str(Path.home() / "AudiobookBoss_Output")),
419 |             "output_filename_pattern": self._settings_manager.get_setting("output_filename_pattern", 0),
    |                                                                                                     ^^^^ E501
420 |             "output_bitrate": self._settings_manager.get_setting("output_bitrate", 64),
421 |             "output_sample_rate": self._settings_manager.get_setting("output_sample_rate", "auto"),  # Default to auto pass-through
    |

src/abb/controllers/main_controller.py:421:101: E501 Line too long (131 > 100)
    |
419 |             "output_filename_pattern": self._settings_manager.get_setting("output_filename_pattern", 0),
420 |             "output_bitrate": self._settings_manager.get_setting("output_bitrate", 64),
421 |             "output_sample_rate": self._settings_manager.get_setting("output_sample_rate", "auto"),  # Default to auto pass-through
    |                                                                                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E501
422 |             "output_channels": self._settings_manager.get_setting("output_channels", 1),  # 1 = mono
423 |             "output_create_subdirectory": self._settings_manager.get_setting("output_create_subdirectory", True),
    |

src/abb/controllers/main_controller.py:423:101: E501 Line too long (113 > 100)
    |
421 |             "output_sample_rate": self._settings_manager.get_setting("output_sample_rate", "auto"),  # Default to auto pass-through
422 |             "output_channels": self._settings_manager.get_setting("output_channels", 1),  # 1 = mono
423 |             "output_create_subdirectory": self._settings_manager.get_setting("output_create_subdirectory", True),
    |                                                                                                     ^^^^^^^^^^^^^ E501
424 |         }
    |

src/abb/controllers/main_controller.py:503:101: E501 Line too long (104 > 100)
    |
501 |         output_settings = {
502 |             'output_directory': self._settings_manager.get_setting('output_directory'),
503 |             'output_filename_pattern': self._settings_manager.get_setting('output_filename_pattern', 0),
    |                                                                                                     ^^^^ E501
504 |             'use_subdirectory_pattern': self._settings_manager.get_setting('output_create_subdirectory', True),
505 |             'output_bitrate': self._settings_manager.get_setting('output_bitrate', 64),
    |

src/abb/controllers/main_controller.py:504:101: E501 Line too long (111 > 100)
    |
502 |             'output_directory': self._settings_manager.get_setting('output_directory'),
503 |             'output_filename_pattern': self._settings_manager.get_setting('output_filename_pattern', 0),
504 |             'use_subdirectory_pattern': self._settings_manager.get_setting('output_create_subdirectory', True),
    |                                                                                                     ^^^^^^^^^^^ E501
505 |             'output_bitrate': self._settings_manager.get_setting('output_bitrate', 64),
506 |             'output_channels': self._settings_manager.get_setting('output_channels', 1),
    |

src/abb/ffmpeg/command_builder.py:1:1: D100 Missing docstring in public module
src/abb/ffmpeg/command_builder.py:6:5: D415 First line should end with a period, question mark, or exclamation point
  |
5 | class FFmpegCommandBuilder:
6 |     """Builds FFmpeg commands for audio processing"""
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ D415
7 |     
8 |     # DEPRECATED: Use UnifiedMetadataHandler.get_metadata_mapping() instead
  |
  = help: Add closing punctuation

src/abb/ffmpeg/command_builder.py:34:9: D415 First line should end with a period, question mark, or exclamation point
   |
33 |     def __init__(self, codec_check_func):
34 |         """Initialize with a function to check codec availability"""
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ D415
35 |         self._codec_available = codec_check_func
36 |         self._metadata_handler = None  # Lazy-loaded when ABB_NEW_META=True
   |
   = help: Add closing punctuation

src/abb/ffmpeg/command_builder.py:47:9: D415 First line should end with a period, question mark, or exclamation point
   |
45 |         ffprobe_exe_path: Optional[str] = None,
46 |     ) -> List[str]:
47 |         """Build complete FFmpeg command for processing"""
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ D415
48 |         cmd = []
49 |         cmd.append(ffmpeg_exe_path)
   |
   = help: Add closing punctuation

src/abb/ffmpeg/command_builder.py:105:9: D415 First line should end with a period, question mark, or exclamation point
    |
103 |         duration_seconds: int = 30,
104 |     ) -> List[str]:
105 |         """Build FFmpeg command for preview generation"""
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ D415
106 |         import tempfile
    |
    = help: Add closing punctuation

src/abb/ffmpeg/command_builder.py:144:9: D415 First line should end with a period, question mark, or exclamation point
    |
143 |     def _apply_metadata_to_command(self, cmd: List[str], metadata: Dict[str, Any]) -> None:
144 |         """Apply metadata tags to FFmpeg command"""
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ D415
145 |         # Check if we should use UnifiedMetadataHandler for mapping
146 |         if self._should_use_unified_handler():
    |
    = help: Add closing punctuation

src/abb/ffmpeg/command_builder.py:218:9: D415 First line should end with a period, question mark, or exclamation point
    |
217 |     def _apply_audio_settings_to_command(self, cmd: List[str], settings: Dict[str, Any]) -> None:
218 |         """Apply audio codec and settings to FFmpeg command"""
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ D415
219 |         audio_codec = "libfdk_aac" if self._codec_available("libfdk_aac") else "aac"
220 |         cmd.extend(["-c:a", audio_codec])
    |
    = help: Add closing punctuation

src/abb/ffmpeg_utils.py:1:1: D205 1 blank line required between summary line and description
  |
1 | / """FFmpeg utilities for Audiobook Boss.
2 | | Handles bundled FFmpeg binaries with libfdk support.
3 | | """
  | |___^ D205
4 |
5 |   import json
  |
  = help: Insert single blank line

src/abb/ffmpeg_utils.py:29:5: D205 1 blank line required between summary line and description
   |
28 |   def get_audio_properties(file_path: str) -> Tuple[Dict, Optional[str]]:
29 | /     """Get audio properties of a file using ffprobe.
30 | |     Caches results per file path to avoid repeated calls.
31 | |
32 | |     Returns a tuple: (properties_dict, error_message_or_none)
33 | |     - properties_dict: Dictionary with bitrate, sample_rate, channels, file_size.
34 | |                      Contains default values if ffprobe fails for some properties.
35 | |     - error_message_or_none: String with error details if ffprobe fails, else None.
36 | |     """
   | |_______^ D205
37 |       global _audio_properties_cache
38 |       error_message: Optional[str] = None
   |
   = help: Insert single blank line

src/abb/ffmpeg_utils.py:82:101: E501 Line too long (112 > 100)
   |
81 |         if result.returncode != 0:
82 |             error_message = f"ffprobe failed with exit code {result.returncode}. Error: {result.stderr.strip()}"
   |                                                                                                     ^^^^^^^^^^^^ E501
83 |             _audio_properties_cache[file_path] = properties
84 |             return properties, error_message
   |

src/abb/ffmpeg_utils.py:104:101: E501 Line too long (135 > 100)
    |
103 | …
104 | … output: {e}. stdout: {result.stdout.strip() if 'result' in locals() else 'N/A'}"
    |                                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E501
105 | …
106 | …path}"
    |

src/abb/ffmpeg_utils.py:120:101: E501 Line too long (120 > 100)
    |
118 |                 warn_props.append(key)
119 |         # if warn_props: # Intentionally commented out print, will be removed by formatter
120 |             # print(f"[AUDIO PROPERTY WARNING] File {file_path} missing or invalid values for: {', '.join(warn_props)}")
    |                                                                                                     ^^^^^^^^^^^^^^^^^^^^ E501
121 |         _audio_properties_cache[file_path] = properties
    |

src/abb/ffmpeg_utils.py:203:5: D103 Missing docstring in public function
    |
203 | def build_ffmpeg_preview_command(
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ D103
204 |     input_file_path: str,
205 |     metadata: Dict[str, Any],
    |

src/abb/ffmpeg_utils.py:216:5: D103 Missing docstring in public function
    |
216 | def build_ffmpeg_command(
    |     ^^^^^^^^^^^^^^^^^^^^ D103
217 |     input_files: List[str],
218 |     output_file_full_path: str,
    |

src/abb/main.py:1:1: D205 1 blank line required between summary line and description
  |
1 | / """Main entry point for Audiobook Boss (ABB).
2 | | Bootstraps QApplication and launches MainWindow.
3 | | """
  | |___^ D205
4 |
5 |   import os
  |
  = help: Insert single blank line

src/abb/main_window.py:1:1: D205 1 blank line required between summary line and description
  |
1 | / """MainWindow class for Audiobook Boss (ABB).
2 | | Inherits QMainWindow, sets default size to 1280x820 (see Style Guide §2).
3 | | """
  | |___^ D205
4 |
5 |   import logging
  |
  = help: Insert single blank line

src/abb/main_window.py:56:9: D107 Missing docstring in `__init__`
   |
54 |     """Main application window."""
55 |
56 |     def __init__(self) -> None:
   |         ^^^^^^^^ D107
57 |         super().__init__()
58 |         self.setWindowTitle("Audiobook Boss")
   |

src/abb/main_window.py:139:101: E501 Line too long (101 > 100)
    |
137 |             f"The essential command-line tool(s) {tool_list_str} could not be found. "
138 |             f"Audiobook Boss needs these tools to process audio files.\n\n"
139 |             f"Please ensure that {tool_list_str} is installed and accessible in your system's PATH, "
    |                                                                                                     ^ E501
140 |             f"or that it is included in the application's bundled resources.\n\n"
141 |             f"You may need to configure the paths in the application settings if they are installed in a custom location."
    |

src/abb/main_window.py:141:101: E501 Line too long (122 > 100)
    |
139 |             f"Please ensure that {tool_list_str} is installed and accessible in your system's PATH, "
140 |             f"or that it is included in the application's bundled resources.\n\n"
141 |             f"You may need to configure the paths in the application settings if they are installed in a custom location."
    |                                                                                                     ^^^^^^^^^^^^^^^^^^^^^^ E501
142 |         )
143 |         msg_box.setStandardButtons(QMessageBox.Ok)
    |

src/abb/main_window.py:182:101: E501 Line too long (121 > 100)
    |
180 |             "output_bitrate": self.settings.value("output_bitrate", 64, type=int),
181 |             "output_channels": self.settings.value("output_channels", 1, type=int), # 1 = mono
182 |             "output_sample_rate": self.settings.value("output_sample_rate", 44100, type=int),  # Auto-detected from input
    |                                                                                                     ^^^^^^^^^^^^^^^^^^^^^ E501
183 |             "output_directory": self.settings.value("output_directory", str(Path.home()), type=str),
184 |             "output_filename_pattern": self.settings.value("output_filename_pattern", 0, type=int),
    |

src/abb/main_window.py:185:101: E501 Line too long (110 > 100)
    |
183 |             "output_directory": self.settings.value("output_directory", str(Path.home()), type=str),
184 |             "output_filename_pattern": self.settings.value("output_filename_pattern", 0, type=int),
185 |             "output_create_subdirectory": self.settings.value("output_create_subdirectory", False, type=bool),
    |                                                                                                     ^^^^^^^^^^ E501
186 |             "use_subdirectory_pattern": self.settings.value("use_subdirectory_pattern", True, type=bool),
187 |             "filename_pattern": self.settings.value("filename_pattern", 0, type=int), # 0 = default pattern
    |

src/abb/main_window.py:186:101: E501 Line too long (105 > 100)
    |
184 |             "output_filename_pattern": self.settings.value("output_filename_pattern", 0, type=int),
185 |             "output_create_subdirectory": self.settings.value("output_create_subdirectory", False, type=bool),
186 |             "use_subdirectory_pattern": self.settings.value("use_subdirectory_pattern", True, type=bool),
    |                                                                                                     ^^^^^ E501
187 |             "filename_pattern": self.settings.value("filename_pattern", 0, type=int), # 0 = default pattern
188 |         }
    |

src/abb/main_window.py:187:101: E501 Line too long (107 > 100)
    |
185 |             "output_create_subdirectory": self.settings.value("output_create_subdirectory", False, type=bool),
186 |             "use_subdirectory_pattern": self.settings.value("use_subdirectory_pattern", True, type=bool),
187 |             "filename_pattern": self.settings.value("filename_pattern", 0, type=int), # 0 = default pattern
    |                                                                                                     ^^^^^^^ E501
188 |         }
189 |         return settings_dict
    |

src/abb/main_window.py:197:101: E501 Line too long (102 > 100)
    |
195 |         self.settings.setValue("output_bitrate", self.app_state["settings"]["output_bitrate"])
196 |         self.settings.setValue("output_channels", self.app_state["settings"]["output_channels"])
197 |         self.settings.setValue("output_sample_rate", self.app_state["settings"]["output_sample_rate"])
    |                                                                                                     ^^ E501
198 |         self.settings.setValue("output_directory", self.app_state["settings"]["output_directory"])
199 |         self.settings.setValue("output_filename_pattern", self.app_state["settings"]["output_filename_pattern"])
    |

src/abb/main_window.py:199:101: E501 Line too long (112 > 100)
    |
197 |         self.settings.setValue("output_sample_rate", self.app_state["settings"]["output_sample_rate"])
198 |         self.settings.setValue("output_directory", self.app_state["settings"]["output_directory"])
199 |         self.settings.setValue("output_filename_pattern", self.app_state["settings"]["output_filename_pattern"])
    |                                                                                                     ^^^^^^^^^^^^ E501
200 |         self.settings.setValue("output_create_subdirectory", self.app_state["settings"]["output_create_subdirectory"])
201 |         self.settings.setValue("use_subdirectory_pattern", self.app_state["settings"]["use_subdirectory_pattern"])
    |

src/abb/main_window.py:200:101: E501 Line too long (118 > 100)
    |
198 |         self.settings.setValue("output_directory", self.app_state["settings"]["output_directory"])
199 |         self.settings.setValue("output_filename_pattern", self.app_state["settings"]["output_filename_pattern"])
200 |         self.settings.setValue("output_create_subdirectory", self.app_state["settings"]["output_create_subdirectory"])
    |                                                                                                     ^^^^^^^^^^^^^^^^^^ E501
201 |         self.settings.setValue("use_subdirectory_pattern", self.app_state["settings"]["use_subdirectory_pattern"])
202 |         self.settings.setValue("filename_pattern", self.app_state["settings"]["filename_pattern"])
    |

src/abb/main_window.py:201:101: E501 Line too long (114 > 100)
    |
199 |         self.settings.setValue("output_filename_pattern", self.app_state["settings"]["output_filename_pattern"])
200 |         self.settings.setValue("output_create_subdirectory", self.app_state["settings"]["output_create_subdirectory"])
201 |         self.settings.setValue("use_subdirectory_pattern", self.app_state["settings"]["use_subdirectory_pattern"])
    |                                                                                                     ^^^^^^^^^^^^^^ E501
202 |         self.settings.setValue("filename_pattern", self.app_state["settings"]["filename_pattern"])
203 |         self.settings.sync()
    |

src/abb/main_window.py:240:101: E501 Line too long (108 > 100)
    |
239 |         self.left_panel_widget = LeftPanelWidget()
240 |         self.left_panel_widget.files_dropped_signal.connect(lambda files: self._controller.add_files(files))
    |                                                                                                     ^^^^^^^^ E501
241 |         self.left_panel_widget.selection_changed_signal.connect(self._on_file_selection_changed)
242 |         self.left_panel_widget.request_remove_signal.connect(self.remove_file)
    |

src/abb/main_window.py:251:9: D205 1 blank line required between summary line and description
    |
250 |       def _on_file_selection_changed(self, index: int) -> None:
251 | /         """Handle file selection changed in the LeftPanelWidget.
252 | |         Delegates to the controller.
253 | |
254 | |         Args:
255 | |             index: Index of the selected file
256 | |         """
    | |___________^ D205
257 |           if index >= 0 and index < len(self.app_state["file_list"]):
258 |               file_path = self.app_state["file_list"][index]
    |
    = help: Insert single blank line

src/abb/main_window.py:263:101: E501 Line too long (139 > 100)
    |
261 | …ed([])
262 | …
263 | …ptional[Dict[str, Any]] = None, metadata: Optional[Dict[str, Any]] = None) -> None:
    |                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E501
264 | …ected file's properties and metadata.
265 | … from the controller.
    |

src/abb/main_window.py:264:9: D205 1 blank line required between summary line and description
    |
263 |       def _update_ui_for_selected_file(self, properties: Optional[Dict[str, Any]] = None, metadata: Optional[Dict[str, Any]] = None) ->…
264 | /         """Update UI elements based on the currently selected file's properties and metadata.
265 | |         This method is intended to be called by a signal from the controller.
266 | |         """
    | |___________^ D205
267 |           is_first_file = False
268 |           if self.app_state["file_list"] and self.left_panel_widget.file_list_widget.currentRow() == 0:
    |
    = help: Insert single blank line

src/abb/main_window.py:268:101: E501 Line too long (101 > 100)
    |
266 |         """
267 |         is_first_file = False
268 |         if self.app_state["file_list"] and self.left_panel_widget.file_list_widget.currentRow() == 0:
    |                                                                                                     ^ E501
269 |             is_first_file = True
    |

src/abb/main_window.py:282:101: E501 Line too long (117 > 100)
    |
280 |             self.left_panel_widget.update_selected_file_properties_display(file_properties)
281 |         else:
282 |             self.left_panel_widget.update_selected_file_properties_display({}) # Clear properties if no file or error
    |                                                                                                     ^^^^^^^^^^^^^^^^^ E501
283 |
284 |         if metadata:
    |

src/abb/main_window.py:314:9: D205 1 blank line required between summary line and description
    |
313 |       def _handle_files_reordered_in_ui(self, new_order_paths: List[str]) -> None:
314 | /         """Handle files reordered signal from LeftPanelWidget.
315 | |         Calls the controller's reorder_files method with the new order.
316 | |
317 | |         Args:
318 | |             new_order_paths: List of file paths in the new order
319 | |         """
    | |___________^ D205
320 |           self._controller.reorder_files(new_order_paths)
    |
    = help: Insert single blank line

src/abb/main_window.py:346:101: E501 Line too long (101 > 100)
    |
344 |         self.file_size_label = QLabel("—")
345 |
346 |         for label in [self.sample_rate_output_label, self.combined_size_label, self.file_size_label]:
    |                                                                                                     ^ E501
347 |             label.setStyleSheet("font-family: 'Menlo', 'Monaco', 'Consolas', 'DejaVu Sans Mono', monospace;")
    |

src/abb/main_window.py:347:101: E501 Line too long (109 > 100)
    |
346 |         for label in [self.sample_rate_output_label, self.combined_size_label, self.file_size_label]:
347 |             label.setStyleSheet("font-family: 'Menlo', 'Monaco', 'Consolas', 'DejaVu Sans Mono', monospace;")
    |                                                                                                     ^^^^^^^^^ E501
348 |
349 |     def remove_file(self, index: int) -> None:
    |

src/abb/main_window.py:350:9: D205 1 blank line required between summary line and description
    |
349 |       def remove_file(self, index: int) -> None:
350 | /         """Remove a file from the list by index.
351 | |         Delegates to the controller.
352 | |
353 | |         Args:
354 | |             index: Index of the file to remove
355 | |         """
    | |___________^ D205
356 |           self._controller.remove_file(index)
    |
    = help: Insert single blank line

src/abb/main_window.py:359:9: D205 1 blank line required between summary line and description
    |
358 |       def _on_add_files_requested(self) -> None:
359 | /         """Handle add files button click from LeftPanelWidget.
360 | |         Opens a file dialog and adds selected files to the file list.
361 | |         """
    | |___________^ D205
362 |           open_dir = self.app_state["settings"]["last_input_dir"]
363 |           file_dialog = QFileDialog(self)
    |
    = help: Insert single blank line

src/abb/main_window.py:374:9: D205 1 blank line required between summary line and description
    |
373 |       def clear_file_list(self) -> None:
374 | /         """Clear all files from the list.
375 | |         Delegates to the controller to handle the operation.
376 | |         """
    | |___________^ D205
377 |           self._controller.clear_file_list()
    |
    = help: Insert single blank line

src/abb/main_window.py:429:101: E501 Line too long (105 > 100)
    |
427 |         self.right_panel_widget = RightPanelWidget(self._controller)
428 |         self.right_panel_widget.populate_settings(self.app_state["settings"])
429 |         # Connect to RightPanelWidget's setting_changed signal (which forwards from OutputSettingsWidget)
    |                                                                                                     ^^^^^ E501
430 |         self.right_panel_widget.setting_changed.connect(self._on_setting_changed)
431 |         self.right_panel_widget.start_processing_requested.connect(self.start_processing)
    |

src/abb/main_window.py:539:9: D205 1 blank line required between summary line and description
    |
538 |       def start_processing(self) -> None:
539 | /         """Start processing the audiobook.
540 | |         Delegates to the controller to handle the processing.
541 | |         """
    | |___________^ D205
542 |           # Set output file path for verification in _processing_done
543 |           output_path = self._controller.get_output_path()
    |
    = help: Insert single blank line

src/abb/main_window.py:547:101: E501 Line too long (101 > 100)
    |
545 |         self._output_file_path = str(Path(output_path) / output_filename)
546 |         
547 |         # Start processing - controller will emit processing_started_signal which triggers UI updates
    |                                                                                                     ^ E501
548 |         self._controller.start_processing()
    |

src/abb/main_window.py:568:101: E501 Line too long (107 > 100)
    |
566 |         self._controller.cover_art_updated_signal.connect(self._on_cover_art_updated_from_controller)
567 |         self._controller.status_message_updated_signal.connect(self._update_status)
568 |         self._controller.processing_started_signal.connect(lambda: self._set_ui_for_processing_start(True))
    |                                                                                                     ^^^^^^^ E501
569 |         self._controller.processing_progress_signal.connect(self._update_progress)
570 |         self._controller.processing_finished_signal.connect(self._processing_done)
    |

src/abb/main_window.py:634:101: E501 Line too long (111 > 100)
    |
632 |         self._set_ui_for_processing_start(False)
633 |         self.right_panel_widget.reset_progress()
634 |         file_path_to_check = output_file_path if output_file_path else getattr(self, "_output_file_path", None)
    |                                                                                                     ^^^^^^^^^^^ E501
635 |
636 |         if file_path_to_check and os.path.exists(file_path_to_check):
    |

src/abb/main_window.py:637:101: E501 Line too long (120 > 100)
    |
636 |         if file_path_to_check and os.path.exists(file_path_to_check):
637 |             self.status_bar.showMessage(f"Processing completed successfully. File saved to: {file_path_to_check}", 5000)
    |                                                                                                     ^^^^^^^^^^^^^^^^^^^^ E501
638 |         else:
639 |             self.status_bar.showMessage("Processing completed but output file not found. Check permissions and disk space.", 5000)
    |

src/abb/main_window.py:639:101: E501 Line too long (130 > 100)
    |
637 |             self.status_bar.showMessage(f"Processing completed successfully. File saved to: {file_path_to_check}", 5000)
638 |         else:
639 |             self.status_bar.showMessage("Processing completed but output file not found. Check permissions and disk space.", 5000)
    |                                                                                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E501
640 |
641 |     def _processing_error(self, error_message: str) -> None:
    |

src/abb/metadata_utils.py:1:1: D205 1 blank line required between summary line and description
  |
1 | / """Metadata utilities for Audiobook Boss.
2 | | Handles extraction of metadata from audio files using mutagen.
3 | | """
  | |___^ D205
4 |   from pathlib import Path
5 |   from typing import Any, Dict, Optional
  |
  = help: Insert single blank line

src/abb/processing_worker.py:1:1: D205 1 blank line required between summary line and description
  |
1 | / """ProcessingWorker class for Audiobook Boss.
2 | | Handles audio processing in a separate thread.
3 | | """
  | |___^ D205
4 |
5 |   import os
  |
  = help: Insert single blank line

src/abb/processing_worker.py:19:5: D205 1 blank line required between summary line and description
   |
18 |   class ProcessingWorker(QObject):
19 | /     """Worker class for processing audiobooks in a separate thread.
20 | |     Inherits from QObject to support signals/slots and moveToThread.
21 | |     """
   | |_______^ D205
22 |
23 |       progress_updated = Signal(int)
   |
   = help: Insert single blank line

src/abb/processing_worker.py:79:101: E501 Line too long (117 > 100)
   |
78 |             ffmpeg_exe_path = self.ffmpeg_path
79 |             if not ffmpeg_exe_path or not os.path.isfile(ffmpeg_exe_path) or not os.access(ffmpeg_exe_path, os.X_OK):
   |                                                                                                     ^^^^^^^^^^^^^^^^^ E501
80 |                 self.error.emit("Failed to start: FFmpeg executable not found or not executable.")
81 |                 return
   |

src/abb/processing_worker.py:128:9: D205 1 blank line required between summary line and description
    |
126 |                             settings: Dict,
127 |                             total_duration_seconds: float) -> None:
128 | /         """Start full audiobook processing.
129 | |         This is a slot that can be called from another thread.
130 | |         
131 | |         Args:
132 | |             file_list: List of input audio file paths
133 | |             output_path: Directory to save the output file
134 | |             output_filename: Name of the output file
135 | |             metadata: Dictionary of metadata to embed
136 | |             settings: Dictionary of audio settings (bitrate, channels, etc.)
137 | |             total_duration_seconds: Total duration of all input files in seconds
138 | |         """
    | |___________^ D205
139 |           self.process(file_list, output_path, output_filename, metadata, settings, total_duration_seconds)
    |
    = help: Insert single blank line

src/abb/processing_worker.py:139:101: E501 Line too long (105 > 100)
    |
137 |             total_duration_seconds: Total duration of all input files in seconds
138 |         """
139 |         self.process(file_list, output_path, output_filename, metadata, settings, total_duration_seconds)
    |                                                                                                     ^^^^^ E501
140 |
141 |     @Slot()
    |

src/abb/processing_worker.py:148:9: D205 1 blank line required between summary line and description
    |
146 |                                temp_cover_path: Optional[str] = None,
147 |                                duration_seconds: int = 30) -> None:
148 | /         """Start preview processing.
149 | |         This is a slot that can be called from another thread.
150 | |         
151 | |         Args:
152 | |             input_file_path: Path to the input audio file
153 | |             metadata: Dictionary of metadata to embed
154 | |             settings: Dictionary of audio settings (bitrate, channels, etc.)
155 | |             temp_cover_path: Optional path to a cover art image
156 | |             duration_seconds: Duration of the preview in seconds (default: 30)
157 | |         """
    | |___________^ D205
158 |           if self._is_processing:
159 |               self.error.emit("Processing already in progress")
    |
    = help: Insert single blank line

src/abb/processing_worker.py:201:9: D205 1 blank line required between summary line and description
    |
200 |       def cancel(self) -> None:
201 | /         """Cancel the current processing operation.
202 | |         Implements robust cancel logic: terminate → wait 2s → kill.
203 | |         """
    | |___________^ D205
204 |           if not self._is_processing:
205 |               return
    |
    = help: Insert single blank line

src/abb/processing_worker.py:231:9: D205 1 blank line required between summary line and description
    |
230 |       def _cleanup_after_cancel_or_error(self) -> None:
231 | /         """Clean up after cancellation or error.
232 | |         This method handles both cancelled operations and process errors.
233 | |         """
    | |___________^ D205
234 |           self._cleanup_temp_files()
235 |           if self._output_file and os.path.exists(self._output_file):
    |
    = help: Insert single blank line

src/abb/processing_worker.py:272:101: E501 Line too long (120 > 100)
    |
270 |                 error_output = ""
271 |                 if self._process:
272 |                     error_output = bytes(self._process.readAllStandardError()).decode("utf-8", errors="replace").strip()
    |                                                                                                     ^^^^^^^^^^^^^^^^^^^^ E501
273 |                 specific_error_message = f"FFmpeg failed (exit code {exit_code}, status {exit_status.name})"
274 |                 if error_output:
    |

src/abb/processing_worker.py:273:101: E501 Line too long (108 > 100)
    |
271 |                 if self._process:
272 |                     error_output = bytes(self._process.readAllStandardError()).decode("utf-8", errors="replace").strip()
273 |                 specific_error_message = f"FFmpeg failed (exit code {exit_code}, status {exit_status.name})"
    |                                                                                                     ^^^^^^^^ E501
274 |                 if error_output:
275 |                     specific_error_message += f": {error_output}"
    |

src/abb/services/adapters/base_adapter.py:109:101: E501 Line too long (102 > 100)
    |
107 |         """
108 |         if self._is_destroyed:
109 |             self._logger.warning(f"Attempted to register callback on destroyed adapter: {event_name}")
    |                                                                                                     ^^ E501
110 |             return
    |

src/abb/services/adapters/base_adapter.py:290:101: E501 Line too long (104 > 100)
    |
288 |         self.emit_signal_safe(self.error_occurred, error_type, error_message)
289 |     
290 |     def _handle_callback_error(self, event_name: str, callback: CallbackType, error: Exception) -> None:
    |                                                                                                     ^^^^ E501
291 |         """Handle errors that occur in callbacks.
    |

src/abb/services/file_service.py:38:9: D200 One-line docstring should fit on one line
   |
37 |       def get_combined_size(self) -> int:
38 | /         """Get the combined size of all files in bytes.
39 | |         """
   | |___________^ D200
40 |           total = 0
41 |           for f in self._files:
   |
   = help: Reformat to one line

src/abb/services/interfaces.py:298:9: D107 Missing docstring in `__init__`
    |
296 |     """Base exception for service operations."""
297 |     
298 |     def __init__(self, message: str, category: str = ErrorCategory.PROCESSING_ERROR):
    |         ^^^^^^^^ D107
299 |         super().__init__(message)
300 |         self.message = message
    |

src/abb/services/interfaces.py:303:9: D105 Missing docstring in magic method
    |
301 |         self.category = category
302 |     
303 |     def __str__(self) -> str:
    |         ^^^^^^^ D105
304 |         return f"[{self.category}] {self.message}"
    |

src/abb/services/processing_service.py:49:101: E501 Line too long (103 > 100)
   |
47 |         self._worker_thread.finished.connect(self._worker_thread.deleteLater)
48 |         
49 |         self._output_file = "" # Used to hold the output file path for the 'finished' signal if needed.
   |                                                                                                     ^^^ E501
50 |     
51 |     def process_full(self,
   |

src/abb/services/processing_service.py:68:101: E501 Line too long (109 > 100)
   |
66 |         os.makedirs(output_path, exist_ok=True)
67 |         output_file_full_path = os.path.join(output_path, output_filename)
68 |         self._output_file = output_file_full_path # Storing for potential use, though worker passes path back
   |                                                                                                     ^^^^^^^^^ E501
69 |         
70 |         total_duration = sum(get_duration(file_path) for file_path in input_files)
   |

src/abb/services/processing_service.py:207:101: E501 Line too long (105 > 100)
    |
205 |             self._worker_thread.wait(5000) 
206 |         
207 |         self.status.emit("Processing cancelled") # This might be premature if worker is still cleaning up
    |                                                                                                     ^^^^^ E501
208 |     
209 |     def cleanup_worker_resources(self) -> None:
    |

src/abb/services/service_factory.py:76:101: E501 Line too long (131 > 100)
   |
76 | def create_processing_service(path_service: Optional[IPathService] = None) -> Union[ProcessingService, QtProcessingServiceAdapter]:
   |                                                                                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E501
77 |     """Create a processing service instance based on feature flag.
   |

src/abb/services/service_factory.py:101:101: E501 Line too long (120 > 100)
    |
100 | def create_settings_manager(settings_file_path: str, 
101 |                            default_settings: Optional[dict] = None) -> Union[SettingsManager, QtSettingsManagerAdapter]:
    |                                                                                                     ^^^^^^^^^^^^^^^^^^^^ E501
102 |     """Create a settings manager instance based on feature flag.
    |

src/abb/services/service_factory.py:168:101: E501 Line too long (138 > 100)
    |
166 | …
167 | …
168 | …nal[IPathService] = None) -> Union[ProcessingService, QtProcessingServiceAdapter]:
    |                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E501
169 | …ne.
    |

src/abb/services/service_factory.py:182:101: E501 Line too long (120 > 100)
    |
181 |     def get_settings_manager(self, settings_file_path: str, 
182 |                            default_settings: Optional[dict] = None) -> Union[SettingsManager, QtSettingsManagerAdapter]:
    |                                                                                                     ^^^^^^^^^^^^^^^^^^^^ E501
183 |         """Get cached settings manager or create new one.
    |

src/abb/services/service_factory.py:194:101: E501 Line too long (101 > 100)
    |
192 |         cache_key = f'settings_manager_{settings_file_path}'
193 |         if cache_key not in self._services:
194 |             self._services[cache_key] = create_settings_manager(settings_file_path, default_settings)
    |                                                                                                     ^ E501
195 |         return self._services[cache_key]
    |

src/abb/services/service_factory.py:240:101: E501 Line too long (128 > 100)
    |
240 | def get_processing_service(path_service: Optional[IPathService] = None) -> Union[ProcessingService, QtProcessingServiceAdapter]:
    |                                                                                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E501
241 |     """Get processing service from default factory."""
242 |     return default_factory.get_processing_service(path_service)
    |

src/abb/services/service_factory.py:246:101: E501 Line too long (117 > 100)
    |
245 | def get_settings_manager(settings_file_path: str, 
246 |                         default_settings: Optional[dict] = None) -> Union[SettingsManager, QtSettingsManagerAdapter]:
    |                                                                                                     ^^^^^^^^^^^^^^^^^ E501
247 |     """Get settings manager from default factory."""
248 |     return default_factory.get_settings_manager(settings_file_path, default_settings)
    |

src/abb/ui/dialogs/__init__.py:1:1: D104 Missing docstring in public package
src/abb/ui/dialogs/about_dialog.py:1:1: D100 Missing docstring in public module
src/abb/ui/dialogs/settings_dialog.py:1:1: D100 Missing docstring in public module
src/abb/ui/dialogs/settings_dialog.py:119:101: E501 Line too long (133 > 100)
    |
117 |         self.updated_settings["dark_mode"] = bool(state)
118 |         self.updated_settings["theme"] = "dark" if bool(state) else "light"
119 |         logger.debug(f"Theme setting changed to: {self.updated_settings['theme']} (dark_mode: {self.updated_settings['dark_mode']})")
    |                                                                                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E501
120 |     
121 |     def _on_ffmpeg_path_changed(self, path):
    |

src/abb/ui/widgets/coverart.py:1:1: D205 1 blank line required between summary line and description
  |
1 | / """CoverArtWidget for Audiobook Boss.
2 | | Provides a drag-and-drop area for cover art images with click-to-select functionality.
3 | | """
  | |___^ D205
4 |
5 |   from pathlib import Path
  |
  = help: Insert single blank line

src/abb/ui/widgets/coverart.py:29:5: D205 1 blank line required between summary line and description
   |
28 |   class CoverArtWidget(QWidget):
29 | /     """Widget for displaying and managing cover art.
30 | |     Accepts drag-drop of image files and provides a button to load images.
31 | |     """
   | |_______^ D205
32 |
33 |       # Signals emitted when cover art is changed
   |
   = help: Insert single blank line

src/abb/ui/widgets/coverart.py:138:101: E501 Line too long (110 > 100)
    |
136 |                 return
137 |             print(
138 |                 f"Image loaded successfully. Dimensions: {original_pixmap.width()}x{original_pixmap.height()}"
    |                                                                                                     ^^^^^^^^^^ E501
139 |             )
    |

src/abb/ui/widgets/coverart.py:215:101: E501 Line too long (117 > 100)
    |
213 |                 self.image_label.setPixmap(QPixmap())  # Clear image_label display
214 |                 self.image_label.setText("Too large\nMax 4000x4000")  # Set error message
215 |                 self.image_label.setFixedSize(DEFAULT_PREVIEW_WIDTH, DEFAULT_PREVIEW_HEIGHT)  # Reset to default size
    |                                                                                                     ^^^^^^^^^^^^^^^^^ E501
216 |                 self.coverArtChanged.emit(QPixmap())  # Emit empty on failure
217 |                 return
    |

src/abb/ui/widgets/dropzone.py:1:1: D205 1 blank line required between summary line and description
  |
1 | / """DropZone widget for Audiobook Boss.
2 | | Provides a drag-and-drop area for audio files with click-to-select functionality.
3 | | """
  | |___^ D205
4 |
5 |   from pathlib import Path
  |
  = help: Insert single blank line

src/abb/ui/widgets/dropzone.py:14:5: D205 1 blank line required between summary line and description
   |
13 |   class DropZone(QLabel):
14 | /     """Custom drag-and-drop area for audio files.
15 | |     Accepts .mp3, .m4a, .m4b, and .aac files.
16 | |     """
   | |_______^ D205
17 |
18 |       # Signal emitted when files are dropped or selected
   |
   = help: Insert single blank line

src/abb/ui/widgets/left_panel_widget.py:1:1: D100 Missing docstring in public module
src/abb/ui/widgets/left_panel_widget.py:12:5: D205 1 blank line required between summary line and description
   |
11 |   class LeftPanelWidget(QWidget):
12 | /     """Widget for the left panel of the main window.
13 | |     Contains a drop zone for files and a list of files.
14 | |     """
   | |_______^ D205
15 |
16 |       # Signals
   |
   = help: Insert single blank line

src/abb/ui/widgets/left_panel_widget.py:278:9: D205 1 blank line required between summary line and description
    |
277 |       def _handle_files_reordered(self):
278 | /         """Handles the reordering of files in the list widget and emits the signal.
279 | |         This method is called by DraggableListWidget after a drop event.
280 | |         """
    | |___________^ D205
281 |           new_order_paths = []
282 |           for i in range(self.file_list_widget.count()):
    |
    = help: Insert single blank line

src/abb/ui/widgets/left_panel_widget.py:288:5: D200 One-line docstring should fit on one line
    |
287 |   class DraggableListWidget(QListWidget):
288 | /     """Custom QListWidget to handle drag and drop reordering and emit a signal.
289 | |     """
    | |_______^ D200
290 |       def __init__(self, parent=None):
291 |           super().__init__(parent)
    |
    = help: Reformat to one line

src/abb/ui/widgets/left_panel_widget.py:290:9: D107 Missing docstring in `__init__`
    |
288 |     """Custom QListWidget to handle drag and drop reordering and emit a signal.
289 |     """
290 |     def __init__(self, parent=None):
    |         ^^^^^^^^ D107
291 |         super().__init__(parent)
292 |         self.setDragDropMode(QListWidget.InternalMove)
    |

src/abb/ui/widgets/left_panel_widget.py:299:9: D200 One-line docstring should fit on one line
    |
298 |       def dropEvent(self, event):
299 | /         """Handle the drop event for reordering items.
300 | |         """
    | |___________^ D200
301 |           super().dropEvent(event)
302 |           # After the superclass handles the drop, notify the parent LeftPanelWidget
    |
    = help: Reformat to one line

src/abb/ui/widgets/metadata_form_widget.py:1:1: D100 Missing docstring in public module
src/abb/ui/widgets/metadata_form_widget.py:11:5: D205 1 blank line required between summary line and description
   |
10 |   class MetadataFormWidget(QWidget):
11 | /     """Widget for editing metadata of audio files.
12 | |     Contains form fields for various metadata properties and a cover art widget.
13 | |     """
   | |_______^ D205
14 |
15 |       # Signals
   |
   = help: Insert single blank line

src/abb/ui/widgets/metadata_form_widget.py:162:101: E501 Line too long (112 > 100)
    |
160 |         """Connect signals to slots."""
161 |         # Connect form field textChanged signals to handler
162 |         self.title_edit.editingFinished.connect(lambda: self._on_field_changed("title", self.title_edit.text()))
    |                                                                                                     ^^^^^^^^^^^^ E501
163 |         self.artist_edit.editingFinished.connect(lambda: self._on_field_changed("artist", self.artist_edit.text()))
164 |         self.author_edit.editingFinished.connect(lambda: self._on_field_changed("author", self.author_edit.text()))
    |

src/abb/ui/widgets/metadata_form_widget.py:163:101: E501 Line too long (115 > 100)
    |
161 |         # Connect form field textChanged signals to handler
162 |         self.title_edit.editingFinished.connect(lambda: self._on_field_changed("title", self.title_edit.text()))
163 |         self.artist_edit.editingFinished.connect(lambda: self._on_field_changed("artist", self.artist_edit.text()))
    |                                                                                                     ^^^^^^^^^^^^^^^ E501
164 |         self.author_edit.editingFinished.connect(lambda: self._on_field_changed("author", self.author_edit.text()))
165 |         self.album_edit.editingFinished.connect(lambda: self._on_field_changed("album", self.album_edit.text()))
    |

src/abb/ui/widgets/metadata_form_widget.py:164:101: E501 Line too long (115 > 100)
    |
162 |         self.title_edit.editingFinished.connect(lambda: self._on_field_changed("title", self.title_edit.text()))
163 |         self.artist_edit.editingFinished.connect(lambda: self._on_field_changed("artist", self.artist_edit.text()))
164 |         self.author_edit.editingFinished.connect(lambda: self._on_field_changed("author", self.author_edit.text()))
    |                                                                                                     ^^^^^^^^^^^^^^^ E501
165 |         self.album_edit.editingFinished.connect(lambda: self._on_field_changed("album", self.album_edit.text()))
166 |         self.narrator_edit.editingFinished.connect(lambda: self._on_field_changed("narrator", self.narrator_edit.text()))
    |

src/abb/ui/widgets/metadata_form_widget.py:165:101: E501 Line too long (112 > 100)
    |
163 |         self.artist_edit.editingFinished.connect(lambda: self._on_field_changed("artist", self.artist_edit.text()))
164 |         self.author_edit.editingFinished.connect(lambda: self._on_field_changed("author", self.author_edit.text()))
165 |         self.album_edit.editingFinished.connect(lambda: self._on_field_changed("album", self.album_edit.text()))
    |                                                                                                     ^^^^^^^^^^^^ E501
166 |         self.narrator_edit.editingFinished.connect(lambda: self._on_field_changed("narrator", self.narrator_edit.text()))
167 |         self.series_edit.editingFinished.connect(lambda: self._on_field_changed("series", self.series_edit.text()))
    |

src/abb/ui/widgets/metadata_form_widget.py:166:101: E501 Line too long (121 > 100)
    |
164 |         self.author_edit.editingFinished.connect(lambda: self._on_field_changed("author", self.author_edit.text()))
165 |         self.album_edit.editingFinished.connect(lambda: self._on_field_changed("album", self.album_edit.text()))
166 |         self.narrator_edit.editingFinished.connect(lambda: self._on_field_changed("narrator", self.narrator_edit.text()))
    |                                                                                                     ^^^^^^^^^^^^^^^^^^^^^ E501
167 |         self.series_edit.editingFinished.connect(lambda: self._on_field_changed("series", self.series_edit.text()))
168 |         self.series_index_edit.editingFinished.connect(lambda: self._on_field_changed("series_index", self.series_index_edit.text()))
    |

src/abb/ui/widgets/metadata_form_widget.py:167:101: E501 Line too long (115 > 100)
    |
165 |         self.album_edit.editingFinished.connect(lambda: self._on_field_changed("album", self.album_edit.text()))
166 |         self.narrator_edit.editingFinished.connect(lambda: self._on_field_changed("narrator", self.narrator_edit.text()))
167 |         self.series_edit.editingFinished.connect(lambda: self._on_field_changed("series", self.series_edit.text()))
    |                                                                                                     ^^^^^^^^^^^^^^^ E501
168 |         self.series_index_edit.editingFinished.connect(lambda: self._on_field_changed("series_index", self.series_index_edit.text()))
169 |         self.track_number_edit.editingFinished.connect(lambda: self._on_field_changed("track", self.track_number_edit.text()))
    |

src/abb/ui/widgets/metadata_form_widget.py:168:101: E501 Line too long (133 > 100)
    |
166 |         self.narrator_edit.editingFinished.connect(lambda: self._on_field_changed("narrator", self.narrator_edit.text()))
167 |         self.series_edit.editingFinished.connect(lambda: self._on_field_changed("series", self.series_edit.text()))
168 |         self.series_index_edit.editingFinished.connect(lambda: self._on_field_changed("series_index", self.series_index_edit.text()))
    |                                                                                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E501
169 |         self.track_number_edit.editingFinished.connect(lambda: self._on_field_changed("track", self.track_number_edit.text()))
170 |         self.year_edit.editingFinished.connect(lambda: self._on_field_changed("year", self.year_edit.text()))
    |

src/abb/ui/widgets/metadata_form_widget.py:169:101: E501 Line too long (126 > 100)
    |
167 |         self.series_edit.editingFinished.connect(lambda: self._on_field_changed("series", self.series_edit.text()))
168 |         self.series_index_edit.editingFinished.connect(lambda: self._on_field_changed("series_index", self.series_index_edit.text()))
169 |         self.track_number_edit.editingFinished.connect(lambda: self._on_field_changed("track", self.track_number_edit.text()))
    |                                                                                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^ E501
170 |         self.year_edit.editingFinished.connect(lambda: self._on_field_changed("year", self.year_edit.text()))
171 |         self.genre_edit.editingFinished.connect(lambda: self._on_field_changed("genre", self.genre_edit.text()))
    |

src/abb/ui/widgets/metadata_form_widget.py:170:101: E501 Line too long (109 > 100)
    |
168 |         self.series_index_edit.editingFinished.connect(lambda: self._on_field_changed("series_index", self.series_index_edit.text()))
169 |         self.track_number_edit.editingFinished.connect(lambda: self._on_field_changed("track", self.track_number_edit.text()))
170 |         self.year_edit.editingFinished.connect(lambda: self._on_field_changed("year", self.year_edit.text()))
    |                                                                                                     ^^^^^^^^^ E501
171 |         self.genre_edit.editingFinished.connect(lambda: self._on_field_changed("genre", self.genre_edit.text()))
172 |         self.description_edit.editingFinished.connect(lambda: self._on_field_changed("description", self.description_edit.text()))
    |

src/abb/ui/widgets/metadata_form_widget.py:171:101: E501 Line too long (112 > 100)
    |
169 |         self.track_number_edit.editingFinished.connect(lambda: self._on_field_changed("track", self.track_number_edit.text()))
170 |         self.year_edit.editingFinished.connect(lambda: self._on_field_changed("year", self.year_edit.text()))
171 |         self.genre_edit.editingFinished.connect(lambda: self._on_field_changed("genre", self.genre_edit.text()))
    |                                                                                                     ^^^^^^^^^^^^ E501
172 |         self.description_edit.editingFinished.connect(lambda: self._on_field_changed("description", self.description_edit.text()))
    |

src/abb/ui/widgets/metadata_form_widget.py:172:101: E501 Line too long (130 > 100)
    |
170 |         self.year_edit.editingFinished.connect(lambda: self._on_field_changed("year", self.year_edit.text()))
171 |         self.genre_edit.editingFinished.connect(lambda: self._on_field_changed("genre", self.genre_edit.text()))
172 |         self.description_edit.editingFinished.connect(lambda: self._on_field_changed("description", self.description_edit.text()))
    |                                                                                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E501
173 |         
174 |         # Connect cover art changed signals from CoverArtWidget
    |

src/abb/ui/widgets/output_settings_widget.py:1:1: D100 Missing docstring in public module
src/abb/ui/widgets/output_settings_widget.py:15:7: D101 Missing docstring in public class
   |
15 | class OutputSettingsWidget(QWidget):
   |       ^^^^^^^^^^^^^^^^^^^^ D101
16 |     setting_changed_signal = Signal(str, object)
   |

src/abb/ui/widgets/output_settings_widget.py:18:9: D107 Missing docstring in `__init__`
   |
16 |     setting_changed_signal = Signal(str, object)
17 |
18 |     def __init__(self, app_state=None, initial_settings=None, parent=None):
   |         ^^^^^^^^ D107
19 |         super().__init__(parent)
20 |         # Support both app_state and initial_settings for backward compatibility
   |

src/abb/ui/widgets/output_settings_widget.py:111:101: E501 Line too long (106 > 100)
    |
110 |         self.output_dir_edit.setText(self.initial_settings.get("output_directory", ""))
111 |         self.filename_pattern_edit.setText(str(self.initial_settings.get("output_filename_pattern", "0")))
    |                                                                                                     ^^^^^^ E501
112 |         self.subdir_checkbox.setChecked(self.initial_settings.get("output_create_subdirectory", False))
    |

src/abb/ui/widgets/output_settings_widget.py:112:101: E501 Line too long (103 > 100)
    |
110 |         self.output_dir_edit.setText(self.initial_settings.get("output_directory", ""))
111 |         self.filename_pattern_edit.setText(str(self.initial_settings.get("output_filename_pattern", "0")))
112 |         self.subdir_checkbox.setChecked(self.initial_settings.get("output_create_subdirectory", False))
    |                                                                                                     ^^^ E501
113 |
114 |         # Reconnect signals
    |

src/abb/ui/widgets/output_settings_widget.py:127:101: E501 Line too long (107 > 100)
    |
125 |                 new_value = int(new_value)
126 |             except ValueError:
127 |                 # If conversion to int fails, emit the raw string and let SettingsManager handle validation
    |                                                                                                     ^^^^^^^ E501
128 |                 pass
129 |         self.setting_changed_signal.emit(setting_name, new_value)
    |

src/abb/ui/widgets/output_settings_widget.py:158:101: E501 Line too long (113 > 100)
    |
156 |             rate_str = str(sample_rate)
157 |             # Check if rate is in combo box
158 |             if rate_str not in [self.samplerate_combo.itemText(i) for i in range(self.samplerate_combo.count())]:
    |                                                                                                     ^^^^^^^^^^^^^ E501
159 |                 # Add non-standard sample rate
160 |                 self.samplerate_combo.addItem(rate_str)
    |

src/abb/ui/widgets/output_settings_widget.py:170:101: E501 Line too long (102 > 100)
    |
168 |         selected_dir = QFileDialog.getExistingDirectory(self, "Select Output Directory")
169 |         if selected_dir:
170 |             self.output_dir_edit.setText(selected_dir) # This will trigger textChanged and emit signal
    |                                                                                                     ^^ E501
171 |     
172 |     def populate_settings(self, settings):
    |

src/abb/ui/widgets/right_panel_widget.py:1:1: D100 Missing docstring in public module
src/abb/ui/widgets/right_panel_widget.py:20:5: D205 1 blank line required between summary line and description
   |
19 |   class RightPanelWidget(QWidget):
20 | /     """Widget for the right panel of the main window.
21 | |     Contains metadata form, output settings, and processing controls.
22 | |     """
   | |_______^ D205
23 |
24 |       # Signals
   |
   = help: Insert single blank line

Found 122 errors.
No fixes available (9 hidden fixes can be enabled with the `--unsafe-fixes` option).
```

## Test Results & Coverage
============================= test session starts ==============================
platform darwin -- Python 3.12.9, pytest-8.3.5, pluggy-1.5.0
PySide6 6.9.0 -- Qt runtime 6.9.0 -- Qt compiled 6.9.0
rootdir: /Users/<USER>/Projects/ABB_v6
configfile: pyproject.toml
testpaths: tests
plugins: qt-4.4.0, docker-3.1.2, anyio-4.9.0, html-4.1.1, langsmith-0.3.16, metadata-3.1.1, cov-6.1.1, flask-1.2.0
collected 176 items

tests/features/test_combined_size_display.py ...                         [  1%]
tests/features/test_file_import.py ....                                  [  3%]
tests/features/test_file_properties_display.py ...                       [  5%]
tests/integration/test_file_import_integration.py .                      [  6%]
tests/integration/test_file_properties_integration.py .                  [  6%]
tests/integration/test_golden.py ..s                                     [  8%]
tests/test_smoke.py .                                                    [  9%]
tests/unit/app_features/test_file_reordering.py ......                   [ 12%]
tests/unit/app_features/test_output_settings.py ...                      [ 14%]
tests/unit/controllers/test_main_controller.py .........                 [ 19%]
tests/unit/controllers/test_main_controller_properties.py ...            [ 21%]
tests/unit/ffmpeg/test_command_builder.py .......                        [ 25%]
tests/unit/services/test_file_service.py ....                            [ 27%]
tests/unit/services/test_file_service_combined_size.py ..                [ 28%]
tests/unit/services/test_metadata_service.py .....................       [ 40%]
tests/unit/services/test_path_service.py ...                             [ 42%]
tests/unit/services/test_processing_service_start.py ......              [ 45%]
tests/unit/services/test_settings_manager.py ...........                 [ 51%]
tests/unit/services/test_unified_metadata_handler.py ................... [ 62%]
......                                                                   [ 65%]
tests/unit/test_ffmpeg_processing_command.py .....                       [ 68%]
tests/unit/test_metadata_utils.py ..........                             [ 74%]
tests/unit/test_processing_validator.py .........                        [ 79%]
tests/unit/ui_widgets/test_coverart.py .......                           [ 83%]
tests/unit/ui_widgets/test_dropzone.py ...                               [ 85%]
tests/unit/ui_widgets/test_left_panel_widget.py .                        [ 85%]
tests/unit/ui_widgets/test_metadata_form_widget.py .....                 [ 88%]
tests/unit/ui_widgets/test_output_settings_widget.py ...                 [ 90%]
tests/unit/ui_widgets/test_progress_integration.py .........             [ 95%]
tests/unit/workers/test_processing_worker_state.py ........              [100%]

=============================== warnings summary ===============================
tests/features/test_file_import.py::TestFileImportFeature::test_import_files_via_drag_and_drop
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x125c91fd0>>) from signal "timeout()".
    signal.disconnect(slot)

tests/features/test_file_import.py::TestFileImportFeature::test_import_files_via_controller
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x125c565d0>>) from signal "timeout()".
    signal.disconnect(slot)

tests/features/test_file_properties_display.py::TestFilePropertiesDisplay::test_selecting_file_displays_properties
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x127bb6390>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/services/test_file_service_combined_size.py::TestFileServiceCombinedSize::test_combined_size_signal_emitted_on_add_remove
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x13022f6e0>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/services/test_metadata_service.py::TestMetadataService::test_metadata_service_extract_and_load_metadata_populates_attributes_and_emits_signals
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x13022ddc0>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/services/test_metadata_service.py::TestMetadataService::test_metadata_service_handles_none_cover_art
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x13022c620>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/services/test_metadata_service.py::TestMetadataService::test_metadata_service_update_current_metadata_modifies_internal_state_and_emits_signal
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x13022d550>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/services/test_metadata_service.py::TestMetadataService::test_metadata_service_set_cover_art_updates_internal_state_and_emits_signal
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x13022c590>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/services/test_metadata_service.py::TestMetadataServiceDualPath::test_extract_metadata_flag_disabled
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x130226d50>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/services/test_metadata_service.py::TestMetadataServiceDualPath::test_extract_metadata_flag_enabled
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x13022d040>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/services/test_metadata_service.py::TestMetadataServiceDualPath::test_extract_metadata_both_paths_produce_identical_results
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x130227f50>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/services/test_metadata_service.py::TestMetadataServiceDualPath::test_extract_metadata_both_paths_produce_identical_results
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x1302279e0>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/services/test_metadata_service.py::TestMetadataServiceDualPath::test_apply_defaults_flag_disabled
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x130224110>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/services/test_metadata_service.py::TestMetadataServiceDualPath::test_apply_defaults_flag_enabled
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x127bb7590>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/services/test_metadata_service.py::TestMetadataServiceDualPath::test_apply_defaults_both_paths_produce_identical_results
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x13022d880>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/services/test_metadata_service.py::TestMetadataServiceDualPath::test_apply_defaults_both_paths_produce_identical_results
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x1302107a0>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/services/test_metadata_service.py::TestMetadataServiceDualPath::test_extract_and_load_metadata_flag_disabled
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x130210a40>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/services/test_metadata_service.py::TestMetadataServiceDualPath::test_extract_and_load_metadata_flag_enabled
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x130200e90>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/services/test_metadata_service.py::TestMetadataServiceDualPath::test_extract_and_load_metadata_both_paths_produce_identical_results
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x130200f20>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/services/test_metadata_service.py::TestMetadataServiceDualPath::test_extract_and_load_metadata_both_paths_produce_identical_results
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x13022d940>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/services/test_metadata_service.py::TestMetadataServiceDualPath::test_extract_and_load_metadata_none_cover_art_flag_disabled
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x1301f63f0>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/services/test_metadata_service.py::TestMetadataServiceDualPath::test_extract_and_load_metadata_none_cover_art_flag_enabled
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x1301f7770>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/services/test_processing_service_start.py::TestProcessingServiceStart::test_validation_failure_emits_error
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x1301e2450>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/ui_widgets/test_coverart.py::TestCoverArtWidget::test_cover_art_drop_emits_cover_art_changed_signal
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x1302c9f70>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/ui_widgets/test_coverart.py::TestCoverArtWidget::test_cover_art_button_select_emits_cover_art_changed_signal
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x1302c8dd0>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/ui_widgets/test_metadata_form_widget.py::TestMetadataFormWidget::test_field_edit_emits_metadata_field_changed_signal
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x1302c3110>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/workers/test_processing_worker_state.py::TestProcessingWorkerStateManagement::test_worker_prevents_concurrent_processing
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x1301faf60>>) from signal "timeout()".
    signal.disconnect(slot)

tests/unit/workers/test_processing_worker_state.py::TestProcessingWorkerStateManagement::test_preview_process_respects_state
  /opt/homebrew/Caskroom/miniconda/base/lib/python3.12/site-packages/pytestqt/wait_signal.py:741: RuntimeWarning: Failed to disconnect (<bound method _AbstractSignalBlocker._quit_loop_by_timeout of <pytestqt.wait_signal.SignalBlocker object at 0x13022cbc0>>) from signal "timeout()".
    signal.disconnect(slot)

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
================================ tests coverage ================================
_______________ coverage: platform darwin, python 3.12.9-final-0 _______________

Name                                                      Stmts   Miss  Cover   Missing
---------------------------------------------------------------------------------------
src/__init__.py                                               0      0   100%
src/abb/__init__.py                                           0      0   100%
src/abb/controllers/__init__.py                               0      0   100%
src/abb/controllers/main_controller.py                      221    111    50%   91-141, 174-181, 189, 237, 254, 263-269, 277, 286, 294, 308, 324-326, 332, 336-340, 344-346, 351, 356, 363-371, 384, 393, 417, 432-445, 452-455, 486-528
src/abb/ffmpeg/command_builder.py                           122     44    64%   60-61, 65-70, 76-78, 88, 106-141, 147, 187, 197, 203, 205, 227
src/abb/ffmpeg_utils.py                                     115     58    50%   21, 24-25, 55-58, 86-123, 129, 144-148, 153, 158-163, 168-179, 187, 194-195, 210
src/abb/main.py                                              16     16     0%   5-34
src/abb/main_window.py                                      345     83    76%   48-49, 119, 125-144, 149, 161, 170, 207, 212, 222-225, 261, 295-299, 307-311, 320, 356, 362-371, 377, 441-442, 446-449, 457-458, 463-466, 471-473, 477-479, 484-486, 490-499, 521, 585, 600, 618-619, 628, 637, 643-645, 649, 653
src/abb/metadata_utils.py                                   187     26    86%   52-53, 82, 86, 88, 90, 92, 127, 131, 133, 135, 137, 141-142, 164-167, 185-188, 207-209, 227, 231, 239
src/abb/processing_validator.py                              51      3    94%   59, 82-83
src/abb/processing_worker.py                                220    125    43%   87, 102-104, 111-114, 163-198, 207-217, 221-228, 234-248, 270-283, 291-295, 313-315, 318-324, 328-355, 359-380, 392-403
src/abb/services/__init__.py                                  0      0   100%
src/abb/services/adapters/__init__.py                        11     11     0%   20-33
src/abb/services/adapters/base_adapter.py                   180    180     0%   7-420
src/abb/services/adapters/file_service_adapter.py            93     93     0%   8-260
src/abb/services/adapters/metadata_handler_adapter.py       149    149     0%   7-304
src/abb/services/adapters/processing_service_adapter.py     138    138     0%   7-330
src/abb/services/adapters/settings_manager_adapter.py        27     27     0%   3-131
src/abb/services/core/__init__.py                             8      8     0%   15-24
src/abb/services/core/file_service_core.py                  146    146     0%   7-297
src/abb/services/core/metadata_handler_core.py              174    174     0%   16-429
src/abb/services/core/path_service_core.py                   46     46     0%   8-154
src/abb/services/core/processing_service_core.py            225    225     0%   7-487
src/abb/services/core/settings_manager_core.py               72     72     0%   3-142
src/abb/services/file_service.py                             61      2    97%   44-45
src/abb/services/interfaces.py                               72     72     0%   10-333
src/abb/services/metadata_service.py                         67     10    85%   88-92, 96-100, 138, 162
src/abb/services/path_service.py                             46     19    59%   22-33, 58-67, 71
src/abb/services/processing_service.py                       93     18    81%   185-190, 200-207, 219-220, 237, 245-246, 250-251
src/abb/services/service_factory.py                         101    101     0%   8-263
src/abb/services/settings_manager.py                         56      5    91%   38, 45-46, 54-55
src/abb/services/unified_metadata_handler.py                 66      0   100%
src/abb/ui/__init__.py                                        0      0   100%
src/abb/ui/dialogs/__init__.py                                0      0   100%
src/abb/ui/dialogs/about_dialog.py                           42     42     0%   1-70
src/abb/ui/dialogs/settings_dialog.py                        81     81     0%   1-159
src/abb/ui/widgets/__init__.py                                0      0   100%
src/abb/ui/widgets/coverart.py                              154     45    71%   111-112, 121-122, 128-129, 135-136, 147, 151-152, 178-183, 187, 208, 212-217, 241-242, 246-254, 258-266, 276, 280-282, 297
src/abb/ui/widgets/dropzone.py                               70     12    83%   55, 59-63, 79, 83-102
src/abb/ui/widgets/left_panel_widget.py                     168     22    87%   209-211, 216-221, 226-231, 236-242, 252-254, 301-305
src/abb/ui/widgets/metadata_form_widget.py                  115     10    91%   102, 114, 117, 130, 141-157, 194, 202-204
src/abb/ui/widgets/output_settings_widget.py                100     11    89%   22, 139, 151-165
src/abb/ui/widgets/right_panel_widget.py                    127     19    85%   149-150, 154-155, 161-162, 166-167, 171-172, 224, 241, 249, 257-261, 269, 277, 285
---------------------------------------------------------------------------------------
TOTAL                                                      3965   2204    44%
================= 175 passed, 1 skipped, 28 warnings in 4.16s ==================
